/**
 * AnalysisResult Model
 * Sequelize model for archive.analysis_results table
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AnalysisResult = sequelize.define('AnalysisResult', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
    allowNull: false
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'user_id'
  },
  assessment_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    field: 'assessment_data'
  },
  persona_profile: {
    type: DataTypes.JSONB,
    allowNull: true, // Allow null for failed analyses
    field: 'persona_profile'
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'completed',
    validate: {
      isIn: [['completed', 'processing', 'failed']]
    }
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at'
  }
}, {
  tableName: 'analysis_results',
  schema: 'archive',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  underscored: true,
  indexes: [
    {
      name: 'idx_analysis_results_user_id',
      fields: ['user_id']
    },
    {
      name: 'idx_analysis_results_status',
      fields: ['status']
    },
    {
      name: 'idx_analysis_results_created_at',
      fields: ['created_at']
    },
    {
      name: 'idx_analysis_results_user_created',
      fields: ['user_id', 'created_at']
    }
  ]
});

/**
 * Instance methods
 */

/**
 * Get formatted result for API response
 * @returns {Object} - Formatted result
 */
AnalysisResult.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());

  // Format dates
  if (values.created_at) {
    values.created_at = values.created_at.toISOString();
  }
  if (values.updated_at) {
    values.updated_at = values.updated_at.toISOString();
  }

  return values;
};

/**
 * Class methods for associations
 */
AnalysisResult.associate = function(models) {
  // TODO: Fix association issues
  // AnalysisResult.belongsTo(models.UserProfile, {
  //   foreignKey: 'user_id',
  //   targetKey: 'user_id',
  //   as: 'userProfile'
  // });

  // TODO: Add association with AnalysisJob after fixing circular dependency
  // AnalysisResult.hasMany(models.AnalysisJob, {
  //   foreignKey: 'result_id',
  //   as: 'jobs'
  // });
};

/**
 * Static methods
 */

/**
 * Find results by user ID with pagination
 * @param {String} userId - User ID
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Results with pagination
 */
AnalysisResult.findByUserWithPagination = async function(userId, options = {}) {
  const {
    page = 1,
    limit = 10,
    status,
    sort = 'created_at',
    order = 'DESC'
  } = options;

  const offset = (page - 1) * limit;
  
  const whereClause = { user_id: userId };
  if (status) {
    whereClause.status = status;
  }

  const { count, rows } = await this.findAndCountAll({
    where: whereClause,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [[sort, order.toUpperCase()]],
    raw: false
  });

  const totalPages = Math.ceil(count / limit);

  return {
    results: rows,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  };
};

/**
 * Get user statistics
 * @param {String} userId - User ID
 * @returns {Promise<Object>} - User statistics
 */
AnalysisResult.getUserStats = async function(userId) {
  const [results] = await sequelize.query(`
    SELECT 
      COUNT(*) as total_analyses,
      COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
      COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing,
      COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
      MAX(created_at) as latest_analysis
    FROM archive.analysis_results 
    WHERE user_id = :userId
  `, {
    replacements: { userId },
    type: sequelize.QueryTypes.SELECT
  });

  // Get most common archetype - optimized dengan index
  const [archetypeResult] = await sequelize.query(`
    SELECT
      persona_profile->>'archetype' as archetype,
      COUNT(*) as count
    FROM archive.analysis_results
    WHERE user_id = :userId
      AND status = 'completed'
      AND persona_profile IS NOT NULL
    GROUP BY persona_profile->>'archetype'
    ORDER BY count DESC
    LIMIT 1
  `, {
    replacements: { userId },
    type: sequelize.QueryTypes.SELECT
  });

  return {
    total_analyses: parseInt(results.total_analyses) || 0,
    completed: parseInt(results.completed) || 0,
    processing: parseInt(results.processing) || 0,
    failed: parseInt(results.failed) || 0,
    latest_analysis: results.latest_analysis,
    most_common_archetype: archetypeResult?.archetype || null
  };
};

/**
 * Get demographic analysis with optimized queries
 * @param {Object} filters - Demographic filters
 * @returns {Promise<Object>} - Demographic analysis results
 */
AnalysisResult.getDemographicAnalysis = async function(filters = {}) {
  const { gender, ageRange, schoolOrigin, archetype, limit = 100 } = filters;

  let whereClause = 'ar.status = \'completed\'';
  const replacements = { limit };

  if (gender) {
    whereClause += ' AND up.gender = :gender';
    replacements.gender = gender;
  }

  if (ageRange && ageRange.min && ageRange.max) {
    const currentYear = new Date().getFullYear();
    const maxBirthYear = currentYear - ageRange.min;
    const minBirthYear = currentYear - ageRange.max;

    whereClause += ' AND up.date_of_birth BETWEEN :minDate AND :maxDate';
    replacements.minDate = `${minBirthYear}-01-01`;
    replacements.maxDate = `${maxBirthYear}-12-31`;
  }

  if (schoolOrigin) {
    whereClause += ' AND up.school_origin ILIKE :schoolOrigin';
    replacements.schoolOrigin = `%${schoolOrigin}%`;
  }

  if (archetype) {
    whereClause += ' AND ar.persona_profile->>\'archetype\' = :archetype';
    replacements.archetype = archetype;
  }

  // Query yang memanfaatkan composite index idx_analysis_results_demographics
  const results = await sequelize.query(`
    SELECT
      ar.id,
      ar.user_id,
      ar.persona_profile->>'archetype' as archetype,
      ar.status,
      ar.created_at,
      up.gender,
      up.school_origin,
      EXTRACT(YEAR FROM AGE(up.date_of_birth)) as age
    FROM archive.analysis_results ar
    INNER JOIN auth.user_profiles up ON ar.user_id = up.user_id
    WHERE ${whereClause}
    ORDER BY ar.created_at DESC
    LIMIT :limit
  `, {
    replacements,
    type: sequelize.QueryTypes.SELECT
  });

  return results;
};

/**
 * Get archetype distribution with demographic breakdown
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Archetype distribution
 */
AnalysisResult.getArchetypeDistribution = async function(options = {}) {
  const { includeDemo = true, limit = 50 } = options;

  let query = `
    SELECT
      ar.persona_profile->>'archetype' as archetype,
      COUNT(*) as total_count
  `;

  if (includeDemo) {
    query += `,
      COUNT(CASE WHEN up.gender = 'male' THEN 1 END) as male_count,
      COUNT(CASE WHEN up.gender = 'female' THEN 1 END) as female_count,
      ROUND(AVG(EXTRACT(YEAR FROM AGE(up.date_of_birth))), 1) as avg_age,
      COUNT(DISTINCT up.school_origin) as unique_schools
    `;
  }

  query += `
    FROM archive.analysis_results ar
    INNER JOIN auth.user_profiles up ON ar.user_id = up.user_id
    WHERE ar.status = 'completed'
      AND ar.persona_profile->>'archetype' IS NOT NULL
  `;

  if (includeDemo) {
    query += `
      AND up.gender IS NOT NULL
      AND up.date_of_birth IS NOT NULL
    `;
  }

  query += `
    GROUP BY ar.persona_profile->>'archetype'
    ORDER BY total_count DESC
    LIMIT :limit
  `;

  return sequelize.query(query, {
    replacements: { limit },
    type: sequelize.QueryTypes.SELECT
  });
};

/**
 * Get performance metrics for optimized queries
 * @returns {Promise<Object>} - Performance metrics
 */
AnalysisResult.getQueryPerformanceMetrics = async function() {
  // Query untuk mengecek penggunaan index
  const indexUsage = await sequelize.query(`
    SELECT
      schemaname,
      tablename,
      indexname,
      idx_scan as index_scans,
      idx_tup_read as tuples_read,
      idx_tup_fetch as tuples_fetched
    FROM pg_stat_user_indexes
    WHERE schemaname IN ('archive', 'auth')
      AND tablename IN ('analysis_results', 'user_profiles')
    ORDER BY idx_scan DESC
  `, {
    type: sequelize.QueryTypes.SELECT
  });

  // Query untuk mengecek performa table
  const tableStats = await sequelize.query(`
    SELECT
      schemaname,
      tablename,
      seq_scan as sequential_scans,
      seq_tup_read as sequential_tuples_read,
      idx_scan as index_scans,
      idx_tup_fetch as index_tuples_fetched,
      n_tup_ins as inserts,
      n_tup_upd as updates,
      n_tup_del as deletes
    FROM pg_stat_user_tables
    WHERE schemaname IN ('archive', 'auth')
      AND tablename IN ('analysis_results', 'user_profiles')
  `, {
    type: sequelize.QueryTypes.SELECT
  });

  return {
    indexUsage,
    tableStats,
    timestamp: new Date()
  };
};

/**
 * Get demographic analysis with optimized queries
 * @param {Object} filters - Demographic filters
 * @returns {Promise<Object>} - Demographic analysis results
 */
AnalysisResult.getDemographicAnalysis = async function(filters = {}) {
  const { gender, ageRange, schoolOrigin, archetype, limit = 100 } = filters;

  let whereClause = 'ar.status = \'completed\'';
  const replacements = { limit };

  if (gender) {
    whereClause += ' AND up.gender = :gender';
    replacements.gender = gender;
  }

  if (ageRange && ageRange.min && ageRange.max) {
    const currentYear = new Date().getFullYear();
    const maxBirthYear = currentYear - ageRange.min;
    const minBirthYear = currentYear - ageRange.max;

    whereClause += ' AND up.date_of_birth BETWEEN :minDate AND :maxDate';
    replacements.minDate = `${minBirthYear}-01-01`;
    replacements.maxDate = `${maxBirthYear}-12-31`;
  }

  if (schoolOrigin) {
    whereClause += ' AND up.school_origin ILIKE :schoolOrigin';
    replacements.schoolOrigin = `%${schoolOrigin}%`;
  }

  if (archetype) {
    whereClause += ' AND ar.persona_profile->>\'archetype\' = :archetype';
    replacements.archetype = archetype;
  }

  // Query yang memanfaatkan composite index
  const results = await sequelize.query(`
    SELECT
      ar.id,
      ar.user_id,
      ar.persona_profile->>'archetype' as archetype,
      ar.status,
      ar.created_at,
      up.gender,
      up.school_origin,
      EXTRACT(YEAR FROM AGE(up.date_of_birth)) as age
    FROM archive.analysis_results ar
    INNER JOIN auth.user_profiles up ON ar.user_id = up.user_id
    WHERE ${whereClause}
    ORDER BY ar.created_at DESC
    LIMIT :limit
  `, {
    replacements,
    type: sequelize.QueryTypes.SELECT
  });

  return results;
};

module.exports = AnalysisResult;
