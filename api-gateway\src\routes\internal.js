const express = require('express');
const proxy = require('express-http-proxy');
const { authenticateInternalService } = require('../middleware/auth');
const services = require('../config/services');
const logger = require('../utils/logger');

const router = express.Router();

// All internal routes require internal service authentication
router.use(authenticateInternalService);

/**
 * Proxy configuration untuk auth service internal endpoints
 */
const authInternalProxy = proxy(services.auth.url, {
  timeout: services.auth.timeout,
  proxyReqPathResolver: (req) => {
    const path = `/auth${req.url}`;
    logger.info(`Proxying internal auth request to: ${services.auth.url}${path}`);
    return path;
  },
  proxyReqOptDecorator: (proxyReqOpts, srcReq) => {
    // Forward original headers and add internal service context
    proxyReqOpts.headers = {
      ...proxyReqOpts.headers,
      'X-Forwarded-For': srcReq.ip,
      'X-Forwarded-Proto': srcReq.protocol,
      'X-Forwarded-Host': srcReq.get('host'),
      'X-Original-URL': srcReq.originalUrl,
      'X-Service-Key': srcReq.headers['x-service-key'],
      'X-Internal-Service': 'true'
    };
    return proxyReqOpts;
  },
  proxyErrorHandler: (err, res, next) => {
    logger.error('Internal auth service proxy error', {
      error: err.message,
      code: err.code
    });
    
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Internal auth service is temporarily unavailable'
      }
    });
  }
});

/**
 * Proxy configuration untuk archive service internal endpoints
 */
const archiveInternalProxy = proxy(services.archive.url, {
  timeout: services.archive.timeout,
  proxyReqPathResolver: (req) => {
    const path = `/archive${req.url}`;
    logger.info(`Proxying internal archive request to: ${services.archive.url}${path}`);
    return path;
  },
  proxyReqOptDecorator: (proxyReqOpts, srcReq) => {
    // Forward original headers and add internal service context
    proxyReqOpts.headers = {
      ...proxyReqOpts.headers,
      'X-Forwarded-For': srcReq.ip,
      'X-Forwarded-Proto': srcReq.protocol,
      'X-Forwarded-Host': srcReq.get('host'),
      'X-Original-URL': srcReq.originalUrl,
      'X-Service-Key': srcReq.headers['x-service-key'],
      'X-Internal-Service': 'true'
    };
    return proxyReqOpts;
  },
  proxyErrorHandler: (err, res, next) => {
    logger.error('Internal archive service proxy error', {
      error: err.message,
      code: err.code
    });
    
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Internal archive service is temporarily unavailable'
      }
    });
  }
});

// Auth service internal routes
router.post('/auth/verify-token', authInternalProxy);
router.put('/auth/token-balance', authInternalProxy);
router.get('/auth/batch/registration/stats', authInternalProxy);
router.post('/auth/batch/registration/process', authInternalProxy);
router.post('/auth/batch/registration/clear', authInternalProxy);

// Archive service internal routes
router.post('/archive/results', archiveInternalProxy);
router.post('/archive/results/batch', archiveInternalProxy);
router.get('/archive/batch/stats', archiveInternalProxy);
router.post('/archive/batch/process', archiveInternalProxy);
router.post('/archive/batch/clear', archiveInternalProxy);
router.get('/archive/stats/summary', archiveInternalProxy);

// Demographics routes
router.get('/archive/demographics/overview', archiveInternalProxy);
router.get('/archive/demographics/archetype/:archetype', archiveInternalProxy);
router.get('/archive/demographics/schools', archiveInternalProxy);
router.get('/archive/demographics/optimized', archiveInternalProxy);
router.get('/archive/demographics/trends', archiveInternalProxy);

module.exports = router;
