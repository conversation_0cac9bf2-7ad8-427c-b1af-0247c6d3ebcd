{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:57:50","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:57:50","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:57:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:57:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:57:52","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:57:52","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:57:53","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:57:53","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:57:55","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:57:55","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:57:58","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:57:58","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:57:59","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:57:59","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:02","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:02","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:04","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:04","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:05","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:05","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:50","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:50","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:50","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:52","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:52","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:53","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:53","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:56","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:56","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:58:58","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:58:58","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:59:00","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:59:00","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:59:02","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:59:02","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:59:04","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:59:04","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 03:59:06","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 03:59:06","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"3ec93c1f-c1cf-40b4-93ac-28fcb5aa7a02","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1662a1e4-3f2d-4c78-9b41-37c9c30761ad","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8a70cffe-797c-4239-b6ad-53c73638671a","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"3ea16d00-13a7-4078-bf51-8bd6c0ba9915","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"b6c8eb0d-878b-4fc3-bd45-60b2fe70b478","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"0329b899-5a6d-480b-b68e-16cb1e11a67b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"9a1bf534-8b80-4707-8571-6472b1b46b3c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"12f137a6-b705-4daa-b335-4851c992a43e","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"c66bf899-f70a-4c5c-81a1-1f05a4268d98","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"6d24f968-babf-49e6-9994-d786ef48e8fd","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"da3f51d2-0ecd-4ab2-98bc-ffb6eab130a3","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"382b0085-67fe-44e6-891d-259fe744c246","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8b346701-58ab-4915-9f4f-7d1c8b2375f4","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"dd95ec99-dac2-4b90-ab67-f848a6b5cb43","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"2576b237-ccfa-48e4-8a32-ec96e032b9e0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1f4ae85d-65c2-47fb-904a-d8756022a622","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"798a2d3b-6fcd-4b10-9ad5-c9174fe5d9b8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"23cdf369-5fdd-4495-843d-56f0455e5fe2","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"830ce273-bdd4-4a23-bfac-f035543e5308","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"90d7fcad-c2fb-4837-8bd7-b1b2c858fb78","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"4e9f82fa-7018-48cc-80c5-e7c81e0b5bbb","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"c211a64d-6048-4440-ae32-8aa735307902","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"e6d650a9-fc0f-4bef-8ef0-de817334faa1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"58336b36-8a56-450d-ac0c-799d50d9cb47","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"069c12b7-6ab0-4c3a-bff1-00f0acb054e8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"f6824a23-7c56-4c2f-ae3c-3879af18e263","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"e759ef84-9058-4175-b494-33e4d56d150e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3ddbaf46-1092-4206-b5a7-be861d1e8481","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"4bdd6b3b-52b5-4f76-aad2-97dd1f3c13c6","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a5de8645-e005-4026-bb67-2a0594cc54d8","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"73f731b3-2da5-46bc-8544-31bff01b83e5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ed3337b4-aafc-4c71-a8cb-197669a1a009","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"fb3487c7-352a-4e1e-87e8-28561c9cff14","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"ef070185-1435-470f-9860-f39034f66355","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"269a7753-8959-4258-a52e-571bbe8b7c7f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"45179567-e37a-4eba-8cad-7abe8898e4d1","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"525549cd-fd53-4683-b681-3e9459d44cf7","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b8546ed2-1058-4a80-b4dc-d44645efd338","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"3b44ed85-2e7c-40f8-b0dd-9804253d185f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"a4b68d29-3479-4abe-ac09-683919b6692c","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"22ddc4dd-2893-484a-b5f3-30ea9c10839b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d6e67fe7-bd16-4403-9f85-756e54cfb0d9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"041fe171-186e-4877-a672-345f294f79ab","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"0fab7318-4a89-43ab-8251-ca6db502a08e","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"c23c750f-2f79-42fa-8a1c-9316b1ad396d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"7a58c464-898d-4275-94b6-3e4708981d07","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"c3c7217b-1860-4fa2-9b02-2a0b40f62011","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5a5d8185-7fbd-42a3-891e-da389ef716b9","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7a56f892-721f-4b56-a83d-76cf178eb4cc","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"60f70834-0670-4612-9f1f-55976fd05ccc","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"74c4703d-a169-4696-806b-d5dd4e5720a2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"5629836b-f2af-4325-bf25-e1e141480836","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"95e60df6-515e-4252-a458-91e410a3cd0b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"3600d3e4-e3bc-4b2c-ba76-b9c621e4589d","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"15695b21-8abe-47f6-b82c-6e4ad775a762","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"d9b697e6-1526-4184-9692-2435d48d34ee","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"01d68426-b19d-42cc-9937-d9bc853a0013","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"cf287bf2-7461-4f6a-8db1-c0f689c47aef","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"e5ef74c1-ac8c-4c19-9e53-ccaa54a055ff","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:42","userId":"036343cd-9585-40a7-ad1e-2d79621214c6","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"dfa4e98d-dcc2-49b6-8787-3f9450094313","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"77a8e94f-1439-454f-8b98-f1c557e55461","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"48be46c5-67df-4fa6-8d75-4f734bc3617d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e128aed6-e974-4f8b-b552-2d7b7454c427","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"aaba9583-0481-40d5-abf0-e6734f1b64e0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58d2934e-93cd-4a01-a474-13a6fd47831e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"6661106a-6144-4807-8f67-2d8f9b773238","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"8fb8517a-70d1-43aa-ab66-a78cf27bc206","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7bab521b-f54d-4306-8b0e-8d46eac1e8b2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"58be7ce2-d3fb-4627-b6ee-ad06a917b627","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"49e4b2aa-e816-4894-b4f0-643138f38ca8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"361e656a-5383-420c-a2d8-8ba8c24ca0bd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"316d8e1b-ffee-4932-93bf-431c4808802d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"67ce8714-23aa-4eff-819b-1ebe4a801911","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"6a6b678f-f2cc-488a-b2d1-cc89d901c34b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"448b44fa-ee03-42bf-862f-c99f0a65175f","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"2ecb2bf2-d14a-4130-b55e-8d8eedaee0d9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1fdbac12-d395-4595-a2f3-82ddcbd926db","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"a63af58d-56e0-42e0-b891-8f4781a662e5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc590cad-7411-4590-be7f-d8608050b8d9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7ad23b0e-086a-45cf-aca6-cd499e734a61","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"bd2e8229-8da7-4a4e-96de-f45f7095b981","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"36a7c6e3-85ab-48e3-b178-a6c38c10e382","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"6639fe59-8a4d-4626-b04c-34611a1ea867","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"41d26643-9d6d-4aa0-80c2-7d3d8ee8a954","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"7172d5ba-876c-401b-86b9-cd49474d632a","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7f5695a1-95c8-4fd8-8fee-f5854aee6cd0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"abf81515-2aee-4a94-8744-9402e3aef5be","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"202b9313-0786-47ce-9cde-63f9e5a8d684","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"10d4b551-67e8-4128-a7f5-d29b7843f3e0","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"bfb8d445-ac34-4e07-b039-14a19095cab4","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"c869e7e8-012c-4093-93fe-d000a1a2e6b3","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"631f5fe3-94df-4a94-aff2-044e75238cf5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"75ae73c6-e078-461e-a46e-dcec6c9d8a9f","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"75b36ad1-0f9e-4d68-ac1b-d214834e2ff2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"fd48ea6c-6e77-4945-9f18-c0a98354080e","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"16b8d810-8609-4073-aeaa-437ab2ca0d0d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"95718f3a-2e8c-4fef-aade-fd6b43747b1c","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ec8d5671-3f0c-42c5-ba24-0f9022d65bf1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"f1c33912-492b-45a1-accc-019ed2eb4510","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"aa74bcc7-10ba-495e-ba8e-23e859c3de8a","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"66cd9b5d-a29c-4b73-b984-644ba3fc8ecd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:52","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"f136ad7b-9ea9-4206-971a-10ef986ee9a5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"e5c37e76-2872-49ea-bc7a-ea3fd5b7c053","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"e614deed-0c74-4e33-914b-1b2033c8052c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"34277e8b-fc81-4489-8857-dafb56b112d7","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"31fb3b89-2847-4a2f-90d8-d25bf7bbd62a","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8255c864-bd48-47ab-ae18-fddb1bd40fd4","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"b5308dc3-7cff-469c-a457-5c0621cc5358","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"09880e58-9485-4a8c-9280-668019c9807a","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8ed073cb-a2fc-4e00-87fc-d81acb5cda30","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"8be00145-910d-4797-965d-e7fb0983a1c1","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"6cff40f6-4e2b-4a3f-aa58-8d51be672017","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"48c76014-531a-47cb-ab88-47668202155b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"0caacc4c-b4e1-47a3-8759-2a29342fbd33","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"6d9489c9-7b90-40b5-a04d-6fcaf562020c","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"86078253-58f8-4a6d-8747-b8493a9efd26","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"96b96fc0-ddf7-4c81-9131-56953d50b0b5","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"1a13f6ca-ba8b-4ef1-8df9-5835cee275d9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:52","userId":"ec73c5f9-6be8-4ef5-b329-a965b14c593b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"error","message":"Failed to update assessment job status","resultId":"122231a6-87a4-4130-b908-c145f6b17908","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:45:56","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6f877cbd-05ff-49a7-80b9-37262d9243d4","level":"error","message":"Failed to update assessment job status","resultId":"7b1ff74a-6999-4849-84a7-6e94780a8a43","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:45:56","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:12","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:14","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:16","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:20","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:22","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:24","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:26","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:28","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:30","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:12","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:14","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:16","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:20","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:22","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:24","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:26","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:28","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:30","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:12","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:14","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:16","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:20","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:22","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:24","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:26","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:28","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:30","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:12","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:14","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:16","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:20","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:22","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:24","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:26","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:28","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:30","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:12","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:14","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:16","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:20","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:22","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:24","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:26","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:28","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:30","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:12","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:14","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:16","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:20","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:20","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:22","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:22","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:24","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:26","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:26","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:28","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:30","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:30","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:52:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:52:12","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:52:14","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:52:14","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:52:16","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:52:16","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"error","message":"Failed to update assessment job status","resultId":"a2faf77e-5cb7-48a5-94df-50b8876ec173","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"error","message":"Failed to update assessment job status","resultId":"3decde9b-1553-4c2a-aa67-fb4fa9509ee8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"error","message":"Failed to update assessment job status","resultId":"db945d94-0fe6-4b2b-b7c3-653f83431df7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"error","message":"Failed to update assessment job status","resultId":"3350e424-9558-4c20-91f3-7721bd6cbc31","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2e89f414-ad4c-4343-956a-661dba0cb458","level":"error","message":"Failed to update assessment job status","resultId":"64bfa028-5241-45cf-b0b5-b53e6a427a62","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c9e07208-c2c9-458c-9f98-4eade315d816","level":"error","message":"Failed to update assessment job status","resultId":"98d520ed-744b-4610-9ffa-03393a87dd67","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"error","message":"Failed to update assessment job status","resultId":"52e9c317-550f-41ab-9a4d-8edec4ff0a7d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"763d13c6-5d7e-4823-87d5-5d68e4e6315a","level":"error","message":"Failed to update assessment job status","resultId":"ea2f7c83-495f-4fc4-86c7-73e85d23c9cd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"897c7495-3d03-4d36-af2b-eccbda2978a6","level":"error","message":"Failed to update assessment job status","resultId":"503c17b5-c650-48ab-8d5d-6edea0a7babe","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"error","message":"Failed to update assessment job status","resultId":"60e063dc-6bae-4f62-a797-6b06b6ef6ba8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8b77d6b9-ecbc-4a09-81b7-fa0eb6723722","level":"error","message":"Failed to update assessment job status","resultId":"dd2709e6-4189-4c6e-b79b-f4a046885bce","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"error","message":"Failed to update assessment job status","resultId":"ef57a7b4-5799-4f18-af39-ef8e9125197e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d3de4e51-5fa3-44c4-85f3-d115e8885a6f","level":"error","message":"Failed to update assessment job status","resultId":"f9c1159e-c3cf-4e60-8b4b-c93bb09dae9f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"error","message":"Failed to update assessment job status","resultId":"2a73b6ef-949c-4c13-a3cf-e9d37281e7c4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ee46570a-ca2c-41e7-a019-de05a4617869","level":"error","message":"Failed to update assessment job status","resultId":"d6e9c542-52c1-4d9f-a730-79755928513e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cab0b463-81ba-4e0f-87b3-fcbc27f8cd6f","level":"error","message":"Failed to update assessment job status","resultId":"3f2b191a-1848-41b4-9808-c351eed6459e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"54744a3a-eded-4049-af4e-d6992bd3eb3c","level":"error","message":"Failed to update assessment job status","resultId":"31f12ed5-1a1e-4083-b95a-ba0bab124f9d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c95b1e61-e5ad-4b49-bada-740e8d00077f","level":"error","message":"Failed to update assessment job status","resultId":"921aaf90-b24d-48fd-b8f7-8993502df590","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd08e4ac-8130-4327-84c2-eb08e95ad609","level":"error","message":"Failed to update assessment job status","resultId":"51ef2eca-cd6e-4d67-8804-90870ba1390e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0a95dc77-6537-4c73-84f5-cb8461fac504","level":"error","message":"Failed to update assessment job status","resultId":"5d7b10e3-d040-42f9-8429-40e8d284ded0","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"error","message":"Failed to update assessment job status","resultId":"6f82029d-f036-4c51-9fec-a3ca74c9a12d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"error","message":"Failed to update assessment job status","resultId":"f52f6f97-659c-44e8-961a-f9675f28a7a8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"error","message":"Failed to update assessment job status","resultId":"093ab9c4-c599-4a8d-9152-bbaac11204da","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d73d38fb-ae9a-4f84-8e6d-c8e5f5f82503","level":"error","message":"Failed to update assessment job status","resultId":"e79d2bdc-103a-44c8-a372-2988057b66ae","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"error","message":"Failed to update assessment job status","resultId":"511bfac8-8afe-4a8f-bd10-155d156705a9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8a782234-266e-40f4-a95c-9e4c84281005","level":"error","message":"Failed to update assessment job status","resultId":"e4a54ff8-46a9-4879-a64f-84967adad4e9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"68614478-de78-4918-bc02-4427e147f293","level":"error","message":"Failed to update assessment job status","resultId":"8444771d-7b2a-42fd-9203-e1a1859d9614","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c0af8010-74ce-4d9d-b5df-b4c2b1459f9f","level":"error","message":"Failed to update assessment job status","resultId":"27e046e1-638f-4055-9bb1-12526f7c9c18","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b0002eb0-f486-4ba2-917e-89e83fb4ac48","level":"error","message":"Failed to update assessment job status","resultId":"4ea14377-ca15-4cad-8781-e2b2338870d1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"error","message":"Failed to update assessment job status","resultId":"67617e4c-238a-4927-909f-a1d19ef6ea29","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"058f2244-aa37-4151-a6eb-9d1700d10c23","level":"error","message":"Failed to update assessment job status","resultId":"52000f6e-a553-45c3-9e39-da0552248375","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"error","message":"Failed to update assessment job status","resultId":"3a4b51ba-f761-4a62-a6c8-35d744e54873","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8b863bd7-5700-45fc-a252-c6f7719a29a6","level":"error","message":"Failed to update assessment job status","resultId":"d1cd771d-9824-4772-9e7a-e53e8e1d36fa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c0371cc0-86b3-4086-8b4c-0e417c3c0cb2","level":"error","message":"Failed to update assessment job status","resultId":"2fc12c69-0f8f-492f-90a4-37ffd6b29175","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"error","message":"Failed to update assessment job status","resultId":"47000b29-29f4-4d9c-8919-27daaf0503be","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f3e26303-daec-4f96-891b-3dfd3f8dff0a","level":"error","message":"Failed to update assessment job status","resultId":"e664eb3e-0fb5-4ebe-a372-e86e80e6d379","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1a1d8d0b-b97c-4eea-9e3a-e43e674f1d4d","level":"error","message":"Failed to update assessment job status","resultId":"d903e757-b6d0-4487-b3fe-b956ac750acd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"error","message":"Failed to update assessment job status","resultId":"57324e39-d202-4d52-8f4b-490f2db0739d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"85d955ab-7aa7-4571-bffd-b312b491b472","level":"error","message":"Failed to update assessment job status","resultId":"27b7bc15-ebba-475f-8299-057d8a0aa108","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"56098074-2a62-40cb-81c3-48f242fcc2bc","level":"error","message":"Failed to update assessment job status","resultId":"c0723564-18e6-45a2-8f7a-bd26224f0421","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"error","message":"Failed to update assessment job status","resultId":"2a43f6eb-2124-4f11-b5be-5c9f24932748","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"error","message":"Failed to update assessment job status","resultId":"fbf374f2-7974-4f8a-a490-2de07853b2a8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"error","message":"Failed to update assessment job status","resultId":"2c7f1694-4e71-437a-a1d3-c8fbd184377b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5d560730-4b87-4a56-b38a-e025ed65fe92","level":"error","message":"Failed to update assessment job status","resultId":"6d7f2b00-da0e-4559-b6c9-5676b5ce5d43","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"23f1fab8-2962-47bb-ab54-14a2157a8358","level":"error","message":"Failed to update assessment job status","resultId":"e1f96b1c-88c0-4976-99df-205fa671c2de","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"error","message":"Failed to update assessment job status","resultId":"d241572c-4a24-430e-9fe6-bfc1f3beb03d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7fdf47d2-bf8b-4648-9a94-68d296121097","level":"error","message":"Failed to update assessment job status","resultId":"9777b297-f339-440b-9e04-c2128835d610","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"error","message":"Failed to update assessment job status","resultId":"8b86bedb-2471-4d9c-a714-7eee55df9ad1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c23a49e2-7ec6-4003-bd04-62764e27a94c","level":"error","message":"Failed to update assessment job status","resultId":"09c5c8e8-59fc-4eb2-ad25-e6b71dd44007","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"759b4db0-c0b6-4550-b783-d93840c4832d","level":"error","message":"Failed to update assessment job status","resultId":"a68e5372-209b-4242-a7d5-191581184234","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"error","message":"Failed to update assessment job status","resultId":"b565082f-edd2-4ed6-aa3b-d99d0fe2cd28","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"003a23bc-8966-4fb3-97db-2c6626cf90ce","level":"error","message":"Failed to update assessment job status","resultId":"04340051-1fcc-4314-b1cf-897a6020e0ca","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d784eae0-547e-4d68-8fa8-9ae78ed138df","level":"error","message":"Failed to update assessment job status","resultId":"ec40190b-a045-439e-bcc1-5b553a45b42c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b80851e1-4663-4a9a-bc23-bbaf095f5eef","level":"error","message":"Failed to update assessment job status","resultId":"6e1c0fe6-b3cd-448f-a2aa-58bd645b2adc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4f65148b-d9c0-4762-b881-e196ce0cbc8f","level":"error","message":"Failed to update assessment job status","resultId":"733a92a3-244a-41d6-aaa4-be2fddceb450","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"error","message":"Failed to update assessment job status","resultId":"ae87505a-647f-4a3d-9167-c45532e6331c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a8047376-033e-443b-9270-5223123af930","level":"error","message":"Failed to update assessment job status","resultId":"1adcd354-96c7-4706-8bda-e2dc8764db0d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"96e645c6-ab36-44da-9c7c-ee7a37609ff4","level":"error","message":"Failed to update assessment job status","resultId":"d2d3800e-6ae6-41d4-8f18-77b70265fc35","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d534e0ff-c47b-4ad8-91db-b828a4f8bfc0","level":"error","message":"Failed to update assessment job status","resultId":"b59d8957-03ad-4dfd-bbfe-07d7a17f2233","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"323c6297-f514-4fbe-87e3-5e3f82f3ed75","level":"error","message":"Failed to update assessment job status","resultId":"4786db40-a262-4430-a85b-7d214ed81214","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"error","message":"Failed to update assessment job status","resultId":"ab561718-4fb5-4221-8c98-78e56b1da24c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1228ee8b-6eb2-4891-a684-ac911e220179","level":"error","message":"Failed to update assessment job status","resultId":"4f751753-9561-46c3-999b-c88e3a024a6a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a56d53e0-1f50-4a55-a2b3-f085cce6cd3f","level":"error","message":"Failed to update assessment job status","resultId":"15fc45b9-7162-4200-affd-70486e3f1998","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"11cd7c92-bf94-49cc-b303-8c485cd9a832","level":"error","message":"Failed to update assessment job status","resultId":"3eb1b653-dd81-4c84-9c27-339bb3a0632e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"error","message":"Failed to update assessment job status","resultId":"cc0967c2-c7f0-4a03-a8ab-11739697440e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f94d472f-e557-438a-89dc-749612a5bfe5","level":"error","message":"Failed to update assessment job status","resultId":"80747cce-9839-4ecd-9ba8-e06d10f4c247","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"error","message":"Failed to update assessment job status","resultId":"c24330ca-df65-4e27-976e-5f73f8754508","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"error","message":"Failed to update assessment job status","resultId":"e2c82d20-4227-48d1-9fc8-c486cbcd4297","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2e9510a5-5ce5-460c-aa1f-f0da90cb33ef","level":"error","message":"Failed to update assessment job status","resultId":"092f0dbf-23ea-4d73-a72c-968aeab9eb54","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5b5dd7d6-6eb3-4095-8fc6-fdc18def1c5a","level":"error","message":"Failed to update assessment job status","resultId":"d84a4d96-4da1-4c9e-8a24-1730d52a270a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"error","message":"Failed to update assessment job status","resultId":"bbd1b454-d60e-4dc3-ae9b-ad6587cad508","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d0f72ffa-60d4-4555-9bff-35291c012b2f","level":"error","message":"Failed to update assessment job status","resultId":"533dcbd5-6f6e-4fa6-8ef3-273134ad2609","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"error","message":"Failed to update assessment job status","resultId":"dd6382b2-e16f-402f-94fd-f8235dfb44ec","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ae76c4e8-8c61-4606-88b4-7e4d7b98a5c2","level":"error","message":"Failed to update assessment job status","resultId":"35eb0981-dbfd-416b-b723-b048dc434dc3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8789f618-202e-4a16-91b8-4caaaa9ba259","level":"error","message":"Failed to update assessment job status","resultId":"dcdd33ba-f52d-4b6b-bbc8-3d8b382278fc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"error","message":"Failed to update assessment job status","resultId":"6f7a0512-8d8d-4b82-aa3f-2a748a6bbcc2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"error","message":"Failed to update assessment job status","resultId":"b6d656a6-2256-462c-b370-d03a092b0bb0","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"176d4fb7-4fa0-4567-b602-d8a0ecd51fcd","level":"error","message":"Failed to update assessment job status","resultId":"7b136392-06ab-460e-8755-90c2a38034e7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"16a5dbaa-eb08-473c-936e-4bdeab55ef40","level":"error","message":"Failed to update assessment job status","resultId":"a9609fee-3a2f-4fba-ad10-fef2b1e6bb47","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6af95a2a-5f6e-484a-8368-dfdba9182c4b","level":"error","message":"Failed to update assessment job status","resultId":"6dba2657-b7f6-46f7-8e1c-fe0395accd8b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"error","message":"Failed to update assessment job status","resultId":"90ec904b-1218-42b5-b4ab-d53dac289ef6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bdcf6dc6-bc03-4236-ae4f-df4a35519214","level":"error","message":"Failed to update assessment job status","resultId":"474ec69b-a88c-4d25-a39b-f7aa4d66c1cd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0efe9762-1f09-46ad-b5dd-9dc79792f92c","level":"error","message":"Failed to update assessment job status","resultId":"bee380df-bbbb-4696-99f5-14b9e4a74dfc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"error","message":"Failed to update assessment job status","resultId":"75c9aa06-426b-49e5-97a0-d4a8ccf895b6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ea50df04-ad73-4223-bafe-0a1856695600","level":"error","message":"Failed to update assessment job status","resultId":"7952ed6e-646a-4aa1-b0c3-5d1434c0974b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0895581a-efa6-4e93-aa34-cb938760784e","level":"error","message":"Failed to update assessment job status","resultId":"b47ee816-7d72-41ab-9af8-da346655e4bc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6e7f1f9f-a441-4834-831d-f1e8b8428aab","level":"error","message":"Failed to update assessment job status","resultId":"0b25f52a-2b61-47c6-a6ee-ab68ece5a2de","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"error","message":"Failed to update assessment job status","resultId":"afed0481-4ee6-4986-97cb-b31c3a2d2e63","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"error","message":"Failed to update assessment job status","resultId":"e9986490-be74-4af9-b856-548839d02ddb","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"error","message":"Failed to update assessment job status","resultId":"577d3b95-8db7-494d-9da4-e921d739bbf4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"35cf4c1e-40de-4a28-9bc3-38c4df0da5dc","level":"error","message":"Failed to update assessment job status","resultId":"abcf0be1-9a02-4031-89a2-b102e2e92a1d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"error","message":"Failed to update assessment job status","resultId":"d4468e2c-31bf-41b0-a08f-c604accf34c1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8e489fcf-15e6-42ac-ae70-91df9ef4b79f","level":"error","message":"Failed to update assessment job status","resultId":"30ab7b84-35cb-42d6-b182-9e6d9c765e61","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a90d2ead-3a02-4649-aab0-90613be62ded","level":"error","message":"Failed to update assessment job status","resultId":"9756e2e6-e054-4e4c-9ecf-8f65d7f8b538","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bfd0688d-4688-4806-8b3f-8a4d6bf6a480","level":"error","message":"Failed to update assessment job status","resultId":"bcdd42c8-a466-4abd-bcd1-6a371d0c03d2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1abf4924-f7c6-4c71-b0d4-2ddd3949bc06","level":"error","message":"Failed to update assessment job status","resultId":"5a2c7b69-3d31-43cb-ae9d-e19fe1726720","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"error","message":"Failed to update assessment job status","resultId":"b3bf54ae-92c8-4194-b6e8-3355e066022f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"error","message":"Failed to update assessment job status","resultId":"6f241d8d-6def-4f8c-b34f-252690cd5f5c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"39e2e79e-0095-4d02-a965-fd6d8b6745e5","level":"error","message":"Failed to update assessment job status","resultId":"f54b9109-f644-4db0-a3bc-14900dfb8ec2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fca71f8e-be37-42ce-8ddd-54efaf0c0305","level":"error","message":"Failed to update assessment job status","resultId":"76be991e-23bd-4c19-a311-c88c26c6109e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"error","message":"Failed to update assessment job status","resultId":"4d92e99c-3d52-4339-91c2-91889ce92ffa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"error","message":"Failed to update assessment job status","resultId":"a260d348-d67a-49a9-9e1c-0afcebe4a4dc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"error","message":"Failed to update assessment job status","resultId":"8a69889d-debc-43ee-a84b-d20b0a7f326d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"error","message":"Failed to update assessment job status","resultId":"7b4025fe-0fcc-4b9a-9f2d-fffd9adaf169","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"33fe6fbd-8149-44f3-b9d4-c34f29056661","level":"error","message":"Failed to update assessment job status","resultId":"726efff9-2b2e-4da8-878a-d98c10d46791","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"700badf5-a01f-452f-847d-0a382385321f","level":"error","message":"Failed to update assessment job status","resultId":"48e86c9a-4b00-4cdc-999b-f8de0324a9dd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1b73c639-72ba-434c-98d5-23dd69d5ff4d","level":"error","message":"Failed to update assessment job status","resultId":"37ce2c20-188f-48d8-a349-e4862e373850","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"71c650aa-c8f2-4608-9b39-2de058058acb","level":"error","message":"Failed to update assessment job status","resultId":"21069d20-cd61-4b7e-b6c8-56da8304d862","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"302e25d2-48ef-45ee-9315-0baea18f7cce","level":"error","message":"Failed to update assessment job status","resultId":"f513b537-9e88-4571-bcdb-2eaffdeaffad","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"error","message":"Failed to update assessment job status","resultId":"fafd71e7-536b-46b2-a525-64fae60bf50d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"254cff0a-b87b-4006-9fae-a6846194a02c","level":"error","message":"Failed to update assessment job status","resultId":"bf263d7d-9107-4086-a12f-ef63b261754e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"65b335e0-783b-4e0d-857f-32b15a016ecf","level":"error","message":"Failed to update assessment job status","resultId":"fda76644-4ac4-488a-97aa-6a2f709e73e1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"error","message":"Failed to update assessment job status","resultId":"1545f714-228b-4ca2-bdde-3e0164a64101","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5cfb02e5-1cb1-465c-a56a-24a63444dca7","level":"error","message":"Failed to update assessment job status","resultId":"3f21a64c-3ee4-4d56-82ac-de0f7cf94dc3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"error","message":"Failed to update assessment job status","resultId":"339a1ee7-1422-445b-a9f1-e866390672e3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"error","message":"Failed to update assessment job status","resultId":"8bdc340a-81f9-492b-a1df-7c5aa6acc503","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5c66ebea-8e02-4836-aed6-bb347f738498","level":"error","message":"Failed to update assessment job status","resultId":"5e36ca12-c68e-4533-84f2-f1b1b5ef59e6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"de0b02cc-fd04-4226-9cff-0e290e51cab3","level":"error","message":"Failed to update assessment job status","resultId":"805d908f-6c1c-4f02-b429-54f9e876617f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3898587f-2b57-4914-b68f-d26e7aa25e36","level":"error","message":"Failed to update assessment job status","resultId":"f7c942be-b105-452a-a4d9-a5ba407b4add","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6f69dbac-e0e9-4bcc-abca-499112678953","level":"error","message":"Failed to update assessment job status","resultId":"374de024-4c4b-4565-9de3-24324c59f226","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"error","message":"Failed to update assessment job status","resultId":"f7287441-5463-4991-9a8a-ab01e00b142e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5f03aa27-d0ff-4f84-92d5-0d7895b9383e","level":"error","message":"Failed to update assessment job status","resultId":"ad0d5f08-24ec-4ad1-b74e-6269a143d830","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"error","message":"Failed to update assessment job status","resultId":"ddad71ae-4d35-42d2-93f5-1f07fddd0575","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"error","message":"Failed to update assessment job status","resultId":"634d1c6b-e5c1-4091-ac33-5f133a122c6c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"error","message":"Failed to update assessment job status","resultId":"a45060b6-98c5-4af2-96e9-fd4e64df12d1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8ced0430-a81a-4509-a0b8-fa0797f0def8","level":"error","message":"Failed to update assessment job status","resultId":"2ecd0de5-fe67-44f5-a094-0ef4a7b954b7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6791db54-4a47-4739-9c63-d4cd0484041c","level":"error","message":"Failed to update assessment job status","resultId":"03a1e57d-e5dc-48c5-8410-ad43de9a74bb","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"error","message":"Failed to update assessment job status","resultId":"bcaab6ef-fa90-4dcb-9f10-ee0b2eeef918","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f537ebf9-565a-4e6e-b2a1-b53d270d65fd","level":"error","message":"Failed to update assessment job status","resultId":"f51b9d44-6914-409a-b636-e3d9086bc7f7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"80823dab-87dd-44e2-81ad-c855cba6504d","level":"error","message":"Failed to update assessment job status","resultId":"2944ed8c-2cf7-481d-89ec-a7d030360930","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8aa40702-5dff-4bb1-b140-3d7994babc89","level":"error","message":"Failed to update assessment job status","resultId":"43205a39-56fb-44cb-ad4c-bb9115577768","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"43a7c5df-4925-4ffe-b182-c0f1acdca0a9","level":"error","message":"Failed to update assessment job status","resultId":"fab82360-cb58-4811-86d1-d6a3195be36b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"error","message":"Failed to update assessment job status","resultId":"77186715-6283-4c6e-a14c-fa2728a3dfdd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"error","message":"Failed to update assessment job status","resultId":"1acee53d-f6ff-4138-a1ee-b0b1d35e8301","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2e4f9e2a-bfc2-4b14-8a09-bc339888d83f","level":"error","message":"Failed to update assessment job status","resultId":"6ee89eb7-8bfd-4d21-8ace-ffc11ad3fa84","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"876dce37-e2ae-4586-af28-a27153d02e6f","level":"error","message":"Failed to update assessment job status","resultId":"f04e683e-bdef-47ad-b171-1a4a34845bea","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4c801c6c-bf03-410c-b5e5-29a32f20916f","level":"error","message":"Failed to update assessment job status","resultId":"489e0fd2-26eb-4dc7-aa27-d45af2d5703c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8de39c21-469f-4815-9e2e-ea5605e314c2","level":"error","message":"Failed to update assessment job status","resultId":"fd762ee9-d70a-4214-aa47-f487b378bca2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"80a555a7-0c77-4be5-bebf-e3e54bdaa44b","level":"error","message":"Failed to update assessment job status","resultId":"e35b1621-f4d6-49c1-868d-0da6b891c49c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"error","message":"Failed to update assessment job status","resultId":"63bc57d8-9181-4ce0-b83b-cffebd309081","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"error","message":"Failed to update assessment job status","resultId":"f4364216-6998-4c4a-8457-d8b0ed230687","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fb248a07-1959-4896-898a-1635ea1c53a7","level":"error","message":"Failed to update assessment job status","resultId":"43eeea55-3311-4cdf-bf04-a042a17820c3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"error","message":"Failed to update assessment job status","resultId":"2fc9a496-14d2-4b95-93a5-33d1d4c19e88","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"error","message":"Failed to update assessment job status","resultId":"ca14f6ad-d5be-4ad3-a3e6-94a943212daa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"39f36d15-204e-4a68-bd5d-96db156f8b1a","level":"error","message":"Failed to update assessment job status","resultId":"659761a5-e450-4f0d-a239-d6dfe5a7284e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cdd93233-d21c-4f6e-92d3-30a210006551","level":"error","message":"Failed to update assessment job status","resultId":"c32428c8-42c2-44fc-962c-7c30bce7ed86","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"error","message":"Failed to update assessment job status","resultId":"ae2d9678-291c-4c6b-b856-6a6274226641","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4bd24d67-e977-441d-b904-f014c3b20ea5","level":"error","message":"Failed to update assessment job status","resultId":"11c42544-941c-426d-b27c-044ff5b5a147","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"error","message":"Failed to update assessment job status","resultId":"0afd120e-1050-477b-9636-69630c315f06","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9a8e06b8-ec50-46bd-9dd5-1087dc479b67","level":"error","message":"Failed to update assessment job status","resultId":"766be453-f4fb-4616-b56d-237c55b7730f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"92a712fb-7deb-4efb-9a3f-39ef536faff0","level":"error","message":"Failed to update assessment job status","resultId":"f8abb9f8-abed-4a29-9881-aca8b4bc5551","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"error","message":"Failed to update assessment job status","resultId":"995ca57a-6d52-40b5-b2fd-b658df893642","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"error","message":"Failed to update assessment job status","resultId":"60d9e808-caf4-4cda-a6d0-0e43d3e8c505","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"05c66650-08a7-4259-a4ce-79001eec487e","level":"error","message":"Failed to update assessment job status","resultId":"a2189216-5e85-4ee9-ae83-b5bb3cf627d6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0d67bdb7-280c-47f7-a1b7-b2305f7ae412","level":"error","message":"Failed to update assessment job status","resultId":"aa18bf7e-91dc-427d-9b3f-0a6d31eeae6a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f8b6eda5-308a-4cb8-b2f3-c48bca6a3be0","level":"error","message":"Failed to update assessment job status","resultId":"5a274783-bf74-4c72-992f-cd5d0c59f379","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ce0722a5-ba13-466b-a110-07079842184b","level":"error","message":"Failed to update assessment job status","resultId":"a93e1b08-bdc8-412e-8985-39cb35fbb949","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"03c5683b-85b1-4f48-87ae-6560b12eea53","level":"error","message":"Failed to update assessment job status","resultId":"39726812-fb30-436e-9ad2-d9f599b2ca55","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6c35863e-f5d5-4beb-ba71-e897602f5b2e","level":"error","message":"Failed to update assessment job status","resultId":"72b308bf-6602-47f8-90cc-0dc481e8b18c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"52471d80-25bb-468a-a559-ade58ecda87f","level":"error","message":"Failed to update assessment job status","resultId":"d787341d-4a21-487e-bc7a-147aaf1739ec","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"error","message":"Failed to update assessment job status","resultId":"3de5140c-f69d-4dd3-af6a-dfc837427444","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"error","message":"Failed to update assessment job status","resultId":"347af590-b758-45da-bd23-2c0ecb65749e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9af17339-98de-4cc8-ac55-0b337af44de4","level":"error","message":"Failed to update assessment job status","resultId":"dca6008a-2941-49aa-aefa-9f261fd53937","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"dee072f9-b245-40af-ab65-4915e0422590","level":"error","message":"Failed to update assessment job status","resultId":"fb58689b-c1e3-42fb-a6ca-da1eba8c33b8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0f3e2de2-7304-4210-83ea-e2a976a78d7b","level":"error","message":"Failed to update assessment job status","resultId":"d39f8b17-a4b2-4004-a454-e69452a75358","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"error","message":"Failed to update assessment job status","resultId":"2073c336-101a-4461-9ebf-02dfef310e01","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"10d4139e-430a-4c88-8ac7-ab26542ad893","level":"error","message":"Failed to update assessment job status","resultId":"b5b7e41a-1a96-4e89-be69-b58c25c3243c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"error","message":"Failed to update assessment job status","resultId":"53668c63-27bb-4ed5-88ff-350fed93ce57","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"error","message":"Failed to update assessment job status","resultId":"9eeae94b-ca69-4671-9879-2d88a4d60488","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"error","message":"Failed to update assessment job status","resultId":"d3ded056-581d-4854-a97c-bfa2ea3ddc17","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"40250134-f7f1-4336-a926-b3370e1ec348","level":"error","message":"Failed to update assessment job status","resultId":"2c26bbd2-b68f-4b54-9894-6378c5c46ea3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"af4e21e9-59ce-45af-8540-557ed257f6c7","level":"error","message":"Failed to update assessment job status","resultId":"a1924020-e2f1-47c0-87c0-9ee3fe4ee131","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6dd85459-581b-4e05-a9d7-676b326e60a9","level":"error","message":"Failed to update assessment job status","resultId":"80dd512e-7de1-4d1e-86e5-5b5d95667060","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"error","message":"Failed to update assessment job status","resultId":"2da96bee-2d55-48fc-bfd2-e45d4fd19987","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"db152854-8892-43f8-a5d8-252d60712195","level":"error","message":"Failed to update assessment job status","resultId":"e427d029-25ee-4271-9e7a-be795e79bf3a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2eaf19d8-5377-44f4-8e88-63eddf62f434","level":"error","message":"Failed to update assessment job status","resultId":"ed50a581-f241-424c-af53-cfc51f155dae","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"error","message":"Failed to update assessment job status","resultId":"24720b92-6b11-4b45-a2a9-f72819afd9aa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"error","message":"Failed to update assessment job status","resultId":"8bcc9f4f-59ce-4887-b511-fcf480095a4e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a14bd076-2518-4fd1-8cd5-e2bd942bb2da","level":"error","message":"Failed to update assessment job status","resultId":"3a2b6890-1770-47f2-be72-08b5f1a37a24","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bf3b93c9-b6b8-4fc8-b954-9e12d7dae574","level":"error","message":"Failed to update assessment job status","resultId":"8db84115-2c8a-41c2-8929-27bafc867d25","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b46383e1-8f24-40a7-83e3-06d8fbf0d90a","level":"error","message":"Failed to update assessment job status","resultId":"74bc0760-0c23-4b47-ac5e-56d22f013509","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4fba6168-2760-45b4-8b36-564a7c0942cd","level":"error","message":"Failed to update assessment job status","resultId":"fdb9574a-9d0b-471f-95a2-cc3af2c42321","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cd0bffd7-ae10-4807-a4e5-911ea39d59e9","level":"error","message":"Failed to update assessment job status","resultId":"4da4b077-c507-4e86-a387-5237a2733d15","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"error","message":"Failed to update assessment job status","resultId":"c8456793-5dcb-4bfa-8f71-14bcab541aca","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"error","message":"Failed to update assessment job status","resultId":"fa49d4eb-03b5-4654-9017-9502fe1a6cf9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"error","message":"Failed to update assessment job status","resultId":"22013a10-ea4e-4c01-bf1a-0d5c41381dcf","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6d907d77-f811-47ba-ab35-18119b280e92","level":"error","message":"Failed to update assessment job status","resultId":"032113cd-a5c2-474e-bbb9-5d4d3e83b6db","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"error","message":"Failed to update assessment job status","resultId":"dc912dc3-06d3-43b6-8f3e-d7b320c3d97d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"efcd9c0a-d4f3-4b1c-bcfd-64f11f1e8990","level":"error","message":"Failed to update assessment job status","resultId":"15bfe9f9-ce42-42f7-a3f8-78b7c9defe25","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"error","message":"Failed to update assessment job status","resultId":"1c34f04e-5ff6-47a3-b31c-aecc5954ef28","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b002617c-13f3-4660-83ed-9d47e7217934","level":"error","message":"Failed to update assessment job status","resultId":"7b632631-ad02-47e5-b1cf-f1c63e6572b1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"error","message":"Failed to update assessment job status","resultId":"1fbe7416-6087-44fd-8e7b-0a2f6d574d12","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e0236b83-db80-448d-a7ab-6f71cb598d99","level":"error","message":"Failed to update assessment job status","resultId":"59ff3532-d98c-44b9-9ed4-f53e0d54a410","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"error","message":"Failed to update assessment job status","resultId":"8944f0c9-f801-4657-9999-9fc31e0bbdc1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"error","message":"Failed to update assessment job status","resultId":"d0157f04-0c84-40a5-bef4-a5a7920a5b06","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1b768bb4-1c27-440d-a06c-f283d28570bc","level":"error","message":"Failed to update assessment job status","resultId":"3bc56910-398b-4486-b4ce-5b91306e305e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"84a0e3e4-7af4-45a2-a659-9dd450f7cf3a","level":"error","message":"Failed to update assessment job status","resultId":"748e6480-eb35-4a7c-9633-167cfa47bea6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9bd2ab97-f94d-4809-a592-2530fe0e8212","level":"error","message":"Failed to update assessment job status","resultId":"2bf0d60a-74d5-4bf0-b267-7f8d58167f26","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"75237458-d9ed-4022-912f-eb721586e617","level":"error","message":"Failed to update assessment job status","resultId":"fe26b914-71e0-4df6-9e43-6a8f1a1766f3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"error","message":"Failed to update assessment job status","resultId":"14edc3d8-796b-4b88-b75b-8f62fcc04db6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4d471519-7a79-4a74-b55f-e8dc008c1f7d","level":"error","message":"Failed to update assessment job status","resultId":"d39dfc9e-eb36-4687-af0b-acd942368e40","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"error","message":"Failed to update assessment job status","resultId":"961b8287-9ba0-4a6a-94b7-e9bf50979624","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"97484739-4e9b-423f-a398-4167a9eb53bb","level":"error","message":"Failed to update assessment job status","resultId":"d29dafd8-6147-4d83-82fa-05fbe99cf130","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2b124f7a-4af2-49e3-8f42-609bbebf0a03","level":"error","message":"Failed to update assessment job status","resultId":"fbf6a9d3-9e41-4b5b-b117-653c32a707c7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7d50b8be-8e0f-4a93-bddc-efa272844106","level":"error","message":"Failed to update assessment job status","resultId":"d9354cf2-dcd9-416e-8ad1-a2a74c2a4b91","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"error","message":"Failed to update assessment job status","resultId":"542832be-fe7f-4bd6-94c9-57dcc21f9a3b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"error","message":"Failed to update assessment job status","resultId":"1103a046-5e10-4d76-9909-74f45a0c80db","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"49ddf0d7-a87f-46bb-bd9f-c711329859af","level":"error","message":"Failed to update assessment job status","resultId":"b5810a24-7583-4bed-a56d-1391bec24fc3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"error","message":"Failed to update assessment job status","resultId":"2e150eb3-df16-4aa4-a508-1c4d7e1ccd35","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f06f7c94-a3e3-449e-8916-f58139c71afa","level":"error","message":"Failed to update assessment job status","resultId":"6acb882c-aed5-4eb4-9522-f0c98d778f92","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"error","message":"Failed to update assessment job status","resultId":"9c04d28f-0c2d-42eb-885c-24388bf44011","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"error","message":"Failed to update assessment job status","resultId":"66e80017-e0f5-4d36-98d0-721c82ee3775","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"19a27133-405f-4f92-b444-dea66347f53f","level":"error","message":"Failed to update assessment job status","resultId":"289c3085-81db-41eb-a96d-4701748cbbd4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a31b35c1-b3dc-4534-ab3e-e340b5ea3aab","level":"error","message":"Failed to update assessment job status","resultId":"fd94b2d4-d8c7-4018-b34f-960c3020cd68","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c94ebe88-34c1-4d11-ba48-bab6487239ac","level":"error","message":"Failed to update assessment job status","resultId":"7d79a6c8-5ae8-4404-8be8-42cee261aed4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"error","message":"Failed to update assessment job status","resultId":"3ca09deb-26d0-4c96-b20c-9493b370d272","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"error","message":"Failed to update assessment job status","resultId":"66a36d08-12ef-432d-ad9c-0f4c7ce978e7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9fecd68d-e3c4-4cdb-a771-c6787ff341ee","level":"error","message":"Failed to update assessment job status","resultId":"40e01c5f-a637-4997-ae85-3d632657c9b5","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"83f68e4b-063b-4d34-9a5e-82e11c951ec7","level":"error","message":"Failed to update assessment job status","resultId":"8161a870-1c4f-4a94-9aac-43ae2e102c86","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"02580852-7738-4d2a-9429-0bfdadf34a95","level":"error","message":"Failed to update assessment job status","resultId":"afddb60c-baea-40e5-86de-ef7b6a5d12d9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"error","message":"Failed to update assessment job status","resultId":"b952a2fc-c54e-4782-add1-c207364bb66f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0d9f8589-937f-462f-bb42-12a509e5930d","level":"error","message":"Failed to update assessment job status","resultId":"34421a0f-4eb8-43dd-81e7-b2380aa21781","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1861c16f-da99-461e-bcbc-06dc11d07985","level":"error","message":"Failed to update assessment job status","resultId":"5871010f-8f7e-4279-888c-493743489016","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3d34d914-a8a6-4f62-bf95-00e3e403e59e","level":"error","message":"Failed to update assessment job status","resultId":"986d2cfd-eb70-4371-ba83-6dfa2bc90d48","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"error","message":"Failed to update assessment job status","resultId":"11b39a6e-4213-4a0b-b6a5-bd38fafa7923","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"error","message":"Failed to update assessment job status","resultId":"c67ae4bc-9593-4eaf-bd14-cd843bfa0861","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e64c608a-139b-46ee-8341-ce75dee8dc8c","level":"error","message":"Failed to update assessment job status","resultId":"49fd6611-586a-4bef-a1d9-47171f108113","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cb909a07-4a5b-4e1d-b625-33c2498270eb","level":"error","message":"Failed to update assessment job status","resultId":"c20c6be8-c317-4ac4-9234-19ec3a3676e3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e65e3db9-9492-43f9-b534-6da894c1d873","level":"error","message":"Failed to update assessment job status","resultId":"c7c0284e-8aa9-43ec-8f1e-b0fab115e50e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"error","message":"Failed to update assessment job status","resultId":"ff37436b-fe49-423d-9a40-1f58e6ebe618","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"71c71a90-2af6-4070-8a25-e28f3017fcda","level":"error","message":"Failed to update assessment job status","resultId":"91e36104-e1cc-4e64-8d77-ec87bd52bbeb","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"error","message":"Failed to update assessment job status","resultId":"c1c8c4ee-4baa-43be-870f-8f8f0a05ae65","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3a21f70f-751d-42e5-ab55-08baf38a5c94","level":"error","message":"Failed to update assessment job status","resultId":"65b82551-646b-473b-895d-8df1f34d32c9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"98c9de38-0041-4b3c-93ab-787bcf0f0461","level":"error","message":"Failed to update assessment job status","resultId":"b2209853-7ff7-4766-94a4-48fdf8eb760a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"error","message":"Failed to update assessment job status","resultId":"dc182638-703b-4f3b-be78-c6792801550c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"error","message":"Failed to update assessment job status","resultId":"eb8a2ff5-94d1-4fd7-800d-2292919e6faa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cffcbed6-81e4-4928-af91-8d822ed525b1","level":"error","message":"Failed to update assessment job status","resultId":"3e27d77c-84b7-4a13-b99a-a5da283001b3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e0501a88-877e-4663-bb28-146f4ef79dfd","level":"error","message":"Failed to update assessment job status","resultId":"0f96311d-7073-4a64-8d53-cf64f4649442","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f38da3df-ab58-4ada-bb3d-24a684dfbd1b","level":"error","message":"Failed to update assessment job status","resultId":"a05476ae-67cf-4a7a-ba7b-01959ab181ce","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"error","message":"Failed to update assessment job status","resultId":"6f783c47-8db0-4a94-8319-178e0c8cb5dd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"error","message":"Failed to update assessment job status","resultId":"0d1da394-357d-47f4-8590-0a657973d661","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"82d7a09e-498e-4e29-ac31-b7d7757ac2cf","level":"error","message":"Failed to update assessment job status","resultId":"dbc94552-8aa1-43d5-b880-b35c96df2bcf","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"098bfa70-d095-4855-bc54-72f41a95ec31","level":"error","message":"Failed to update assessment job status","resultId":"ceda7ea1-6671-4a14-8b44-1b76fae5ac1c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"error","message":"Failed to update assessment job status","resultId":"c83ebccb-caa7-4002-8112-780fa0d607d0","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8e91f453-6770-4f4b-a039-99c7139f7063","level":"error","message":"Failed to update assessment job status","resultId":"1491c007-b3f9-4ecc-91c5-23356c25f84e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1576fc3c-c5b4-4c65-ada8-765a68e0377b","level":"error","message":"Failed to update assessment job status","resultId":"36729b6f-aa62-40d0-9c1a-1cac49160bd8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"07a6350d-bab2-49ed-b424-42ee80cc5e09","level":"error","message":"Failed to update assessment job status","resultId":"f18db25e-67cc-4838-95e8-02576915156c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"error","message":"Failed to update assessment job status","resultId":"76b16398-ebf0-4524-856d-b9967a4b94fb","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"02727f75-6f30-4d2e-9415-5c4e9d0519cc","level":"error","message":"Failed to update assessment job status","resultId":"9b746d4b-406b-4904-9856-a12766cdc48a","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"error","message":"Failed to update assessment job status","resultId":"6d8b5de9-04fb-41e8-8d68-7dc7f89117b7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"error","message":"Failed to update assessment job status","resultId":"3e59c2f0-092a-4532-8505-c790324dfdd8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"error","message":"Failed to update assessment job status","resultId":"21e2dac1-a612-4a7f-b9d6-2d673e3e49ce","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4df0b48b-bf87-4a71-9780-057dbe46ca1d","level":"error","message":"Failed to update assessment job status","resultId":"8d2f7577-c7ae-4dfe-933c-a9e69f4cb6ed","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d2dc174a-5c32-48de-8f5a-7453cc3f1ff4","level":"error","message":"Failed to update assessment job status","resultId":"48f2b281-b50d-4410-80f8-833f3ef504f2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"error","message":"Failed to update assessment job status","resultId":"35c8b106-6c08-49cd-87cc-f7c9ac9fb22f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"error","message":"Failed to update assessment job status","resultId":"183288d5-269f-41d6-aece-4bfc50067d91","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"error","message":"Failed to update assessment job status","resultId":"933b5591-61d2-45a2-828f-4469abbd3272","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"error","message":"Failed to update assessment job status","resultId":"f68e6121-190c-4876-a1df-86e80c9e3dab","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a3143daa-c561-4c04-8d57-d071062fc2c5","level":"error","message":"Failed to update assessment job status","resultId":"86ee2d46-dbc3-45e4-a642-1897e32b7722","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"21dd0533-4884-4fcd-99a3-2c56ab0b228b","level":"error","message":"Failed to update assessment job status","resultId":"6e180fdf-ccf2-462e-b19f-b9a3745b6743","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b9fd3a9a-2777-43df-9bc7-0704cffd9d96","level":"error","message":"Failed to update assessment job status","resultId":"169f5b55-9077-4ee1-aae1-b020c7bfd2f9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9e7133c2-3896-4873-84c0-3f7ade5868d2","level":"error","message":"Failed to update assessment job status","resultId":"b1d9633d-b72b-415a-b33d-3ab52eac68c4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2833ba0d-a055-4c20-a636-021ed2f9574b","level":"error","message":"Failed to update assessment job status","resultId":"499e9e6b-88f0-4e92-80c9-bec958a1a376","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"98d9fbe7-83ce-46f4-b8cc-47d3b47bd615","level":"error","message":"Failed to update assessment job status","resultId":"53066aa8-7194-4dab-a159-4e9c25c659fa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"error","message":"Failed to update assessment job status","resultId":"06ee719f-f5a6-4551-9573-c29637eb230b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9b2c3007-3347-46db-8d15-47d90153165a","level":"error","message":"Failed to update assessment job status","resultId":"a858378e-b29b-48af-a90b-90e5ebe49f2c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"error","message":"Failed to update assessment job status","resultId":"e22852e0-c2bd-46eb-99ce-c60682003171","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9e50ffb6-7f70-4aa9-a6d7-be90a00813a3","level":"error","message":"Failed to update assessment job status","resultId":"87646938-093c-4a60-8a12-e0cfcd85605f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9922d213-53f5-4af3-ab87-6add4ec9d055","level":"error","message":"Failed to update assessment job status","resultId":"31fbcba1-66b3-4dc3-a9b9-e9df8461383d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4e9b8512-3ba5-4047-ba27-3c905a3844db","level":"error","message":"Failed to update assessment job status","resultId":"215d4663-d9ff-40a8-a381-ecc28db65908","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ac5512be-380a-4189-96a9-95f85709e625","level":"error","message":"Failed to update assessment job status","resultId":"83576c1d-86aa-40ac-88c8-23d478eb1646","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"error","message":"Failed to update assessment job status","resultId":"185ca825-020e-4c88-821d-eb0a830c29ba","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"58e46105-6f4f-4df2-9a14-089d307df5ca","level":"error","message":"Failed to update assessment job status","resultId":"c47f5715-aea0-4823-8a49-2deca3bc4326","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"21159ce2-e828-41a1-a675-cc62b64753e2","level":"error","message":"Failed to update assessment job status","resultId":"6ec861b6-39e5-4e4e-9ff9-19d7e785e531","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1df2127e-4e04-4a34-b350-8d32168d96d9","level":"error","message":"Failed to update assessment job status","resultId":"9d2d9290-38c2-4e5d-b317-bd62232a5d27","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ae36d6c7-48cb-43e6-ad0e-fa5dfbc8d8ef","level":"error","message":"Failed to update assessment job status","resultId":"7362fad1-6996-466d-be97-6a2aba03662d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:57:08","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b872ef3c-77c3-4854-9ab5-922104521e29","level":"error","message":"Failed to update assessment job status","resultId":"7bd4d5c7-f601-49dd-bf2e-9f34afe433ff","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 06:03:02","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cfbe79df-c48b-4c90-a2c4-f10db6827799","level":"error","message":"Failed to update assessment job status","resultId":"ebd28449-6bd0-45b2-9b99-a3c37212842b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 06:24:10","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a544dff2-5980-45fc-b484-749acd1609c3","level":"error","message":"Failed to update assessment job status","resultId":"6f7627b6-04fd-4ea8-8fa2-6922ee128e6d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 06:25:57","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d5937810-02bb-4330-b221-1f593d4cee33","level":"error","message":"Failed to update assessment job status","resultId":"d8176d6b-d8e2-42f7-9425-9b90569f5cae","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 06:51:11","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f23648df-92eb-4ab2-8295-c3aa01e0e95f","level":"error","message":"Failed to update assessment job status","resultId":"e6aa3c14-3269-4a39-ad67-432198d3f11f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 06:56:17","version":"1.0.0"}
{"error":"got status: 503 Service Unavailable. {\"error\":{\"code\":503,\"message\":\"The model is overloaded. Please try again later.\",\"status\":\"UNAVAILABLE\"}}","jobId":"1a95f7d7-9bfa-4210-93e8-fe94b9a69f27","level":"error","message":"Failed to generate persona profile","service":"analysis-worker","timestamp":"2025-07-19 14:32:24","version":"1.0.0"}
{"error":"got status: 503 Service Unavailable. {\"error\":{\"code\":503,\"message\":\"The model is overloaded. Please try again later.\",\"status\":\"UNAVAILABLE\"}}","level":"error","maxRetries":3,"message":"All retry attempts failed for AI persona generation","service":"analysis-worker","timestamp":"2025-07-19 14:32:24","version":"1.0.0"}
{"error":"got status: 503 Service Unavailable. {\"error\":{\"code\":503,\"message\":\"The model is overloaded. Please try again later.\",\"status\":\"UNAVAILABLE\"}}","jobId":"1a95f7d7-9bfa-4210-93e8-fe94b9a69f27","level":"error","message":"Assessment processing failed","service":"analysis-worker","stack":"ServerError: got status: 503 Service Unavailable. {\"error\":{\"code\":503,\"message\":\"The model is overloaded. Please try again later.\",\"status\":\"UNAVAILABLE\"}}\n    at throwErrorIfNotOK (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\node_modules\\@google\\genai\\dist\\node\\index.js:6296:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\node_modules\\@google\\genai\\dist\\node\\index.js:6109:13\n    at async Models.generateContent (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\node_modules\\@google\\genai\\dist\\node\\index.js:2874:20)\n    at async Object.generatePersonaProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\services\\aiService.js:259:22)\n    at async withRetry (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\utils\\errorHandler.js:134:14)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\processors\\assessmentProcessor.js:32:30","timestamp":"2025-07-19 14:32:24","userId":"ff3a4719-6afb-4dd4-b4c9-fcd8a77efd89","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"1a95f7d7-9bfa-4210-93e8-fe94b9a69f27","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-19 14:32:26","userId":"ff3a4719-6afb-4dd4-b4c9-fcd8a77efd89","version":"1.0.0"}
{"error":"Assessment processing failed: got status: 503 Service Unavailable. {\"error\":{\"code\":503,\"message\":\"The model is overloaded. Please try again later.\",\"status\":\"UNAVAILABLE\"}}","jobId":"1a95f7d7-9bfa-4210-93e8-fe94b9a69f27","level":"error","message":"Failed to process assessment job","processingTime":"6008ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 14:32:26","userId":"ff3a4719-6afb-4dd4-b4c9-fcd8a77efd89","version":"1.0.0"}
{"error":"Assessment processing failed: got status: 503 Service Unavailable. {\"error\":{\"code\":503,\"message\":\"The model is overloaded. Please try again later.\",\"status\":\"UNAVAILABLE\"}}","jobId":"1a95f7d7-9bfa-4210-93e8-fe94b9a69f27","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-19 14:32:26","version":"1.0.0"}
{"error":"Request failed with status code 404","jobId":"a69f4d03-a388-4450-8012-a05e784bfd0b","level":"error","message":"Failed to update assessment job status","resultId":"10a9cf53-c850-4e67-9fc5-e38823f944c4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 20:34:04","version":"1.0.0"}
