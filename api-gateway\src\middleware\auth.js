const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

/**
 * Middleware untuk validasi JWT token
 * Mengekstrak user information dari token dan menambahkannya ke req.user
 */
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    logger.warn(`Authentication failed: No token provided for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Access token is required'
      }
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Add user information to request object
    req.user = {
      id: decoded.id,
      email: decoded.email,
      tokenBalance: decoded.tokenBalance || 0
    };

    logger.info(`Authentication successful for user ${decoded.email}`, {
      userId: decoded.id,
      path: req.path,
      method: req.method
    });

    next();
  } catch (error) {
    logger.warn(`Authentication failed: Invalid token for ${req.method} ${req.path}`, {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    let errorMessage = 'Invalid token';
    if (error.name === 'TokenExpiredError') {
      errorMessage = 'Token has expired';
    } else if (error.name === 'JsonWebTokenError') {
      errorMessage = 'Invalid token format';
    }

    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: errorMessage
      }
    });
  }
};

/**
 * Middleware opsional untuk autentikasi
 * Jika token ada, akan divalidasi. Jika tidak ada, request tetap dilanjutkan
 */
const optionalAuth = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return next();
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = {
      id: decoded.id,
      email: decoded.email,
      tokenBalance: decoded.tokenBalance || 0
    };
  } catch (error) {
    // Log error but don't block request
    logger.warn(`Optional auth failed: ${error.message}`, {
      path: req.path,
      method: req.method,
      ip: req.ip
    });
  }

  next();
};

/**
 * Middleware untuk validasi admin JWT token
 * Mengekstrak admin information dari token dan menambahkannya ke req.admin
 */
const authenticateAdminToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    logger.warn(`Admin authentication failed: No token provided for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Access token is required'
      }
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check if token is for admin
    if (decoded.type !== 'admin') {
      logger.warn(`Admin authentication failed: Invalid token type for ${req.method} ${req.path}`, {
        tokenType: decoded.type,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Admin access required'
        }
      });
    }

    // Add admin information to request object
    req.admin = {
      id: decoded.id,
      username: decoded.username,
      email: decoded.email,
      role: decoded.role
    };

    logger.info(`Admin authentication successful for ${decoded.username}`, {
      adminId: decoded.id,
      path: req.path,
      method: req.method,
      role: decoded.role
    });

    next();
  } catch (error) {
    logger.warn(`Admin authentication failed: Invalid token for ${req.method} ${req.path}`, {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    let errorMessage = 'Invalid token';
    if (error.name === 'TokenExpiredError') {
      errorMessage = 'Token has expired';
    } else if (error.name === 'JsonWebTokenError') {
      errorMessage = 'Invalid token format';
    }

    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: errorMessage
      }
    });
  }
};

/**
 * Middleware untuk memeriksa role admin
 * @param {string|Array} allowedRoles - Role yang diizinkan
 * @returns {Function} Middleware function
 */
const requireAdminRole = (allowedRoles) => {
  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

  return (req, res, next) => {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Admin authentication required'
        }
      });
    }

    if (!roles.includes(req.admin.role)) {
      logger.warn('Admin authorization failed: Insufficient role', {
        adminId: req.admin.id,
        username: req.admin.username,
        currentRole: req.admin.role,
        requiredRoles: roles,
        path: req.path
      });

      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions'
        }
      });
    }

    next();
  };
};

/**
 * Middleware untuk validasi internal service authentication
 * Menggunakan X-Service-Key header untuk autentikasi antar service
 */
const authenticateInternalService = (req, res, next) => {
  const serviceKey = req.headers['x-service-key'];
  const isInternalService = req.headers['x-internal-service'];

  if (!serviceKey || !isInternalService) {
    logger.warn('Internal service authentication failed: Missing headers', {
      path: req.path,
      method: req.method,
      ip: req.ip,
      hasServiceKey: !!serviceKey,
      hasInternalFlag: !!isInternalService
    });

    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Internal service authentication required'
      }
    });
  }

  // Validate service key (should match environment variable)
  const expectedServiceKey = process.env.INTERNAL_SERVICE_KEY;
  if (!expectedServiceKey) {
    logger.error('Internal service key not configured', {
      path: req.path,
      method: req.method
    });

    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal service authentication not configured'
      }
    });
  }

  if (serviceKey !== expectedServiceKey) {
    logger.warn('Internal service authentication failed: Invalid service key', {
      path: req.path,
      method: req.method,
      ip: req.ip
    });

    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Invalid service key'
      }
    });
  }

  // Add internal service context to request
  req.internalService = {
    authenticated: true,
    timestamp: new Date().toISOString()
  };

  logger.info('Internal service authenticated successfully', {
    path: req.path,
    method: req.method,
    ip: req.ip
  });

  next();
};

module.exports = {
  authenticateToken,
  optionalAuth,
  authenticateAdminToken,
  requireAdminRole,
  authenticateInternalService
};
