# Troubleshooting Guide - Token Counting System

## Overview

This guide helps diagnose and resolve common issues with the Token Counting and Usage Tracking System.

## Quick Diagnostics

### System Health Check

Run this quick diagnostic to check system status:

```bash
# Check if token counting is enabled
grep ENABLE_TOKEN_COUNTING .env

# Verify API key is set
grep GOOGLE_AI_API_KEY .env

# Run tests to verify functionality
npm test -- --grep "token"

# Check recent logs for errors
tail -n 50 logs/analysis-worker.log | grep -i "token\|error"
```

### Common Symptoms and Quick Fixes

| Symptom | Quick Fix | Section |
|---------|-----------|---------|
| No token usage logs | Check `ENABLE_TOKEN_COUNTING=true` | [Token Counting Not Working](#token-counting-not-working) |
| High response times | Reduce `TOKEN_COUNT_TIMEOUT` | [Performance Issues](#performance-issues) |
| Inaccurate costs | Update pricing configuration | [Cost Calculation Issues](#cost-calculation-issues) |
| Memory issues | Reduce `TOKEN_USAGE_RETENTION_DAYS` | [Memory Issues](#memory-issues) |
| API errors | Check Google AI API key | [API Connection Issues](#api-connection-issues) |

## Detailed Troubleshooting

### Token Counting Not Working

#### Symptoms
- No token usage information in logs
- Missing cost calculations
- Usage statistics show zero values

#### Diagnostic Steps

1. **Check Configuration**
```bash
# Verify token counting is enabled
echo "ENABLE_TOKEN_COUNTING: $ENABLE_TOKEN_COUNTING"

# Check if using mock model
echo "USE_MOCK_MODEL: $USE_MOCK_MODEL"

# Verify API key is set
echo "API Key set: $([ -n "$GOOGLE_AI_API_KEY" ] && echo "Yes" || echo "No")"
```

2. **Check Logs**
```bash
# Look for token counting initialization
grep "TokenCounterService" logs/analysis-worker.log

# Check for token counting attempts
grep "token counting" logs/analysis-worker.log

# Look for configuration errors
grep -i "config\|error" logs/analysis-worker.log
```

3. **Test Token Counting Service**
```javascript
// Test script: test-token-counting.js
const TokenCounterService = require('./src/services/tokenCounterService');
const ai = require('./src/config/ai');

async function testTokenCounting() {
  try {
    ai.initialize();
    const tokenCounter = new TokenCounterService();
    const client = ai.getClient();
    
    const result = await tokenCounter.countInputTokens(
      client,
      'gemini-2.5-flash',
      'Test prompt for token counting',
      'test-job-123'
    );
    
    console.log('Token counting test result:', result);
  } catch (error) {
    console.error('Token counting test failed:', error.message);
  }
}

testTokenCounting();
```

#### Solutions

1. **Enable Token Counting**
```env
ENABLE_TOKEN_COUNTING=true
```

2. **Fix API Configuration**
```env
# Ensure valid API key
GOOGLE_AI_API_KEY=your_actual_api_key_here

# For real API calls (not mock)
USE_MOCK_MODEL=false
```

3. **Check Network Connectivity**
```bash
# Test Google AI API connectivity
curl -H "Authorization: Bearer $GOOGLE_AI_API_KEY" \
     "https://generativelanguage.googleapis.com/v1beta/models"
```

### Performance Issues

#### Symptoms
- Slow AI response times
- High latency in token counting
- Timeout errors in logs

#### Diagnostic Steps

1. **Measure Performance Impact**
```bash
# Check response times in logs
grep "responseTime" logs/analysis-worker.log | tail -10

# Look for timeout errors
grep -i "timeout" logs/analysis-worker.log

# Check token counting duration
grep "token counting completed" logs/analysis-worker.log
```

2. **Monitor Resource Usage**
```bash
# Check memory usage
ps aux | grep node

# Monitor CPU usage
top -p $(pgrep -f "analysis-worker")
```

#### Solutions

1. **Optimize Timeout Settings**
```env
# Reduce timeout for faster failure
TOKEN_COUNT_TIMEOUT=3000

# Enable fallback for reliability
ENABLE_TOKEN_COUNT_FALLBACK=true
```

2. **Adjust Concurrency**
```env
# Reduce worker concurrency if needed
WORKER_CONCURRENCY=5
```

3. **Enable Performance Optimizations**
```env
# Use debug logging to identify bottlenecks
LOG_LEVEL=debug

# Monitor and adjust based on logs
```

### Cost Calculation Issues

#### Symptoms
- Inaccurate cost estimates
- Zero cost calculations
- Unexpected high costs

#### Diagnostic Steps

1. **Verify Pricing Configuration**
```bash
echo "Input token price: $INPUT_TOKEN_PRICE_PER_1K"
echo "Output token price: $OUTPUT_TOKEN_PRICE_PER_1K"
```

2. **Check Token Counts**
```bash
# Look for token count logs
grep "inputTokens\|outputTokens" logs/analysis-worker.log | tail -5
```

3. **Test Cost Calculation**
```javascript
// Test script: test-cost-calculation.js
const TokenCounterService = require('./src/services/tokenCounterService');

const tokenCounter = new TokenCounterService();
const cost = tokenCounter.calculateEstimatedCost(1000, 500);
console.log('Cost for 1000 input + 500 output tokens:', cost);
```

#### Solutions

1. **Update Pricing Configuration**
```env
# Current Gemini 2.5 Flash pricing (as of 2024)
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003
```

2. **Verify Token Counting Accuracy**
```bash
# Run token counting tests
npm test -- --grep "token count"
```

3. **Check for Fallback Usage**
```bash
# Look for fallback usage in logs
grep "fallback" logs/analysis-worker.log
```

### Memory Issues

#### Symptoms
- High memory usage
- Out of memory errors
- Slow performance over time

#### Diagnostic Steps

1. **Check Memory Usage**
```bash
# Monitor Node.js memory usage
node -e "console.log(process.memoryUsage())"

# Check system memory
free -h
```

2. **Analyze Usage Data Size**
```javascript
// Check usage tracker memory usage
const UsageTracker = require('./src/services/usageTracker');
const usageTracker = new UsageTracker();
const stats = usageTracker.getMemoryUsage();
console.log('Usage tracker memory:', stats);
```

#### Solutions

1. **Reduce Data Retention**
```env
# Reduce retention period
TOKEN_USAGE_RETENTION_DAYS=7
```

2. **Optimize Memory Usage**
```javascript
// Force garbage collection periodically
if (global.gc) {
  setInterval(() => {
    global.gc();
  }, 300000); // Every 5 minutes
}
```

3. **Monitor and Clean Up**
```bash
# Run with garbage collection exposed
node --expose-gc src/worker.js
```

### API Connection Issues

#### Symptoms
- API connection errors
- Authentication failures
- Network timeout errors

#### Diagnostic Steps

1. **Test API Connectivity**
```bash
# Test basic connectivity
ping generativelanguage.googleapis.com

# Test API endpoint
curl -H "Authorization: Bearer $GOOGLE_AI_API_KEY" \
     "https://generativelanguage.googleapis.com/v1beta/models"
```

2. **Check API Key Validity**
```javascript
// Test API key
const ai = require('./src/config/ai');
ai.initialize();
const client = ai.getClient();
// If this fails, API key is invalid
```

3. **Review Network Logs**
```bash
# Check for network errors
grep -i "network\|connection\|timeout" logs/analysis-worker.log
```

#### Solutions

1. **Fix API Key**
```env
# Get new API key from Google AI Studio
GOOGLE_AI_API_KEY=your_new_valid_api_key
```

2. **Configure Network Settings**
```env
# Increase timeout for slow networks
TOKEN_COUNT_TIMEOUT=10000

# Enable fallback for unreliable connections
ENABLE_TOKEN_COUNT_FALLBACK=true
```

3. **Check Firewall/Proxy Settings**
```bash
# Test direct connection
curl -v https://generativelanguage.googleapis.com

# Configure proxy if needed
export HTTPS_PROXY=your_proxy_server
```

### Usage Tracking Issues

#### Symptoms
- Missing usage statistics
- Incorrect aggregation
- Data not persisting

#### Diagnostic Steps

1. **Test Usage Tracker**
```javascript
// Test usage tracking
const UsageTracker = require('./src/services/usageTracker');
const usageTracker = new UsageTracker();

// Track test usage
usageTracker.trackUsage('test-job', {
  inputTokens: 100,
  outputTokens: 50,
  totalTokens: 150,
  estimatedCost: 0.01
});

// Check if tracked
const stats = usageTracker.getCurrentUsage();
console.log('Current usage:', stats);
```

2. **Check Data Retention**
```bash
# Look for retention cleanup logs
grep "retention\|cleanup" logs/analysis-worker.log
```

#### Solutions

1. **Verify Configuration**
```env
# Ensure reasonable retention period
TOKEN_USAGE_RETENTION_DAYS=30
```

2. **Reset Usage Data**
```javascript
// Reset if data is corrupted
const usageTracker = new UsageTracker();
usageTracker.resetStats();
```

## Error Code Reference

### Token Counting Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `TOKEN_COUNT_TIMEOUT` | Token counting API timeout | Reduce `TOKEN_COUNT_TIMEOUT` or enable fallback |
| `TOKEN_COUNT_API_ERROR` | Google AI API error | Check API key and connectivity |
| `TOKEN_COUNT_INVALID_RESPONSE` | Invalid API response | Check API version compatibility |
| `TOKEN_COUNT_FALLBACK_USED` | Fallback estimation used | Normal when API is unavailable |

### Usage Tracking Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `USAGE_TRACK_MEMORY_FULL` | Usage data memory limit reached | Reduce retention period |
| `USAGE_TRACK_INVALID_DATA` | Invalid usage data format | Check token data structure |
| `USAGE_TRACK_AGGREGATION_ERROR` | Statistics calculation failed | Reset usage statistics |

### Configuration Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `CONFIG_INVALID_RETENTION` | Invalid retention period | Set `TOKEN_USAGE_RETENTION_DAYS` between 1-365 |
| `CONFIG_INVALID_TIMEOUT` | Invalid timeout value | Set `TOKEN_COUNT_TIMEOUT` between 1000-30000 |
| `CONFIG_INVALID_PRICING` | Invalid pricing configuration | Check pricing values are positive numbers |

## Debug Mode

### Enabling Debug Mode

```env
LOG_LEVEL=debug
```

### Debug Information Available

1. **Token Counting Details**
   - API request/response data
   - Timing information
   - Fallback usage

2. **Usage Tracking Details**
   - Data aggregation steps
   - Memory usage statistics
   - Retention cleanup

3. **Performance Metrics**
   - Response time breakdown
   - Memory allocation
   - CPU usage patterns

### Debug Log Analysis

```bash
# Filter debug logs by component
grep "TokenCounterService" logs/analysis-worker.log | grep "debug"
grep "UsageTracker" logs/analysis-worker.log | grep "debug"

# Analyze performance patterns
grep "responseTime" logs/analysis-worker.log | awk '{print $NF}' | sort -n
```

## Performance Monitoring

### Key Metrics to Monitor

1. **Response Time Impact**
```bash
# Average response time with token counting
grep "responseTime" logs/analysis-worker.log | \
  awk '{sum+=$NF; count++} END {print "Average:", sum/count "ms"}'
```

2. **Token Counting Success Rate**
```bash
# Success rate calculation
total=$(grep "token counting" logs/analysis-worker.log | wc -l)
success=$(grep "token counting completed" logs/analysis-worker.log | wc -l)
echo "Success rate: $(($success * 100 / $total))%"
```

3. **Memory Usage Trends**
```bash
# Memory usage over time
grep "memory usage" logs/analysis-worker.log | \
  awk '{print $1, $2, $NF}' | tail -20
```

### Alerting Thresholds

Set up monitoring alerts for:

- Response time > 5000ms
- Token counting failure rate > 10%
- Memory usage > 500MB
- Cost per hour > expected threshold

## Recovery Procedures

### Emergency Disable

If token counting is causing issues:

```env
# Immediate disable
ENABLE_TOKEN_COUNTING=false
```

### Graceful Degradation

```env
# Enable fallback mode
ENABLE_TOKEN_COUNT_FALLBACK=true

# Reduce timeout for faster failure
TOKEN_COUNT_TIMEOUT=2000
```

### Data Recovery

```javascript
// Recover from corrupted usage data
const UsageTracker = require('./src/services/usageTracker');
const usageTracker = new UsageTracker();

// Backup current data
const backup = usageTracker.exportData();

// Reset and restore clean data
usageTracker.resetStats();
usageTracker.importData(cleanBackupData);
```

## Preventive Measures

### Regular Maintenance

1. **Log Rotation**
```bash
# Set up log rotation
logrotate -f /etc/logrotate.d/analysis-worker
```

2. **Performance Testing**
```bash
# Regular performance tests
npm run test:performance
```

3. **Configuration Validation**
```bash
# Validate configuration weekly
node scripts/validate-config.js
```

### Monitoring Setup

1. **Health Checks**
```javascript
// Automated health check
setInterval(async () => {
  const health = await performHealthCheck();
  if (health.status !== 'healthy') {
    console.error('Health check failed:', health);
  }
}, 300000); // Every 5 minutes
```

2. **Alerting**
```bash
# Set up alerts for critical metrics
# - High error rates
# - Performance degradation
# - Memory leaks
# - API quota issues
```

## Getting Help

### Information to Collect

When reporting issues, include:

1. **Environment Information**
```bash
node --version
npm --version
echo "NODE_ENV: $NODE_ENV"
echo "Token counting enabled: $ENABLE_TOKEN_COUNTING"
```

2. **Recent Logs**
```bash
tail -n 100 logs/analysis-worker.log > debug-logs.txt
```

3. **Configuration**
```bash
# Sanitized configuration (remove API keys)
env | grep -E "(TOKEN_|ENABLE_)" | sed 's/=.*/=***/' > config-debug.txt
```

4. **Test Results**
```bash
npm test -- --grep "token" > test-results.txt 2>&1
```

### Support Checklist

Before seeking help:

- [ ] Checked this troubleshooting guide
- [ ] Reviewed recent logs for errors
- [ ] Verified configuration settings
- [ ] Ran diagnostic tests
- [ ] Attempted basic solutions
- [ ] Collected debug information

This troubleshooting guide covers the most common issues and their solutions. For complex issues, use the debug mode and monitoring tools to gather detailed information before implementing fixes.