# API Gateway Routes Summary

## Overview
Dokumentasi ini merangkum semua routes yang tersedia di API Gateway setelah perbaikan dan penambahan route yang hilang.

## Route Structure

### 1. Auth Routes (`/auth`)
**File**: `src/routes/auth.js`

#### Public Routes (No Authentication)
- `POST /auth/register` - User registration
- `POST /auth/register/batch` - Batch user registration
- `POST /auth/login` - User login

#### Protected Routes (User Authentication Required)
- `GET /auth/profile` - Get user profile
- `PUT /auth/profile` - Update user profile
- `POST /auth/change-password` - Change user password
- `POST /auth/logout` - User logout
- `GET /auth/token-balance` - Get user token balance

### 2. Assessment Routes (`/assessments`)
**File**: `src/routes/assessment.js`

#### User Routes (Authentication Required)
- `POST /assessments/submit` - Submit assessment for analysis
- `GET /assessments/status/:jobId` - Check assessment processing status
- `GET /assessments/queue/status` - Get queue status for monitoring

#### Development Routes (Development Environment Only)
- `POST /assessments/test/submit` - Test assessment submission
- `GET /assessments/test/status/:jobId` - Test assessment status check

#### Service Info
- `GET /assessments/` - Assessment service information

### 3. Archive Routes (`/archive`)
**File**: `src/routes/archive.js`

#### User Routes (Authentication Required)
- `GET /archive/results` - Get user's analysis results with pagination
- `GET /archive/results/:id` - Get specific analysis result
- `PUT /archive/results/:id` - Update analysis result
- `DELETE /archive/results/:id` - Delete analysis result
- `GET /archive/stats` - Get user statistics
- `GET /archive/stats/overview` - Get user statistics overview

### 4. Admin Routes (`/admin`)
**File**: `src/routes/admin.js`

#### Admin Authentication Routes
- `POST /admin/login` - Admin login
- `GET /admin/profile` - Get admin profile (Admin Auth Required)
- `PUT /admin/profile` - Update admin profile (Admin Auth Required)
- `POST /admin/change-password` - Change admin password (Admin Auth Required)
- `POST /admin/logout` - Admin logout (Admin Auth Required)
- `POST /admin/register` - Register new admin (Superadmin Only)

#### Admin User Management Routes
- `GET /admin/users` - Get all users with pagination (Admin/Superadmin)
- `GET /admin/users/:userId` - Get user details (Admin/Superadmin)
- `PUT /admin/users/:userId/token-balance` - Update user token balance (Admin/Superadmin)
- `DELETE /admin/users/:userId` - Delete user (Superadmin Only)

### 5. Internal Service Routes (`/internal`)
**File**: `src/routes/internal.js`
**Authentication**: Internal Service Key Required

#### Auth Service Internal Routes
- `POST /internal/auth/verify-token` - Verify JWT token (internal)
- `PUT /internal/auth/token-balance` - Update token balance (internal)
- `GET /internal/auth/batch/registration/stats` - Batch registration statistics
- `POST /internal/auth/batch/registration/process` - Process registration batch
- `POST /internal/auth/batch/registration/clear` - Clear registration batch queue

#### Archive Service Internal Routes
- `POST /internal/archive/results` - Create analysis result (internal)
- `POST /internal/archive/results/batch` - Batch create analysis results
- `GET /internal/archive/batch/stats` - Batch processing statistics
- `POST /internal/archive/batch/process` - Process batch queue
- `POST /internal/archive/batch/clear` - Clear batch queue
- `GET /internal/archive/stats/summary` - Internal statistics summary

#### Demographics Routes (Internal Only)
- `GET /internal/archive/demographics/overview` - Demographics overview
- `GET /internal/archive/demographics/archetype/:archetype` - Archetype demographics
- `GET /internal/archive/demographics/schools` - School-based analytics
- `GET /internal/archive/demographics/optimized` - Optimized demographics query
- `GET /internal/archive/demographics/trends` - Demographic trends

### 6. Health Routes (`/health`)
**File**: `src/routes/health.js`

- `GET /health` - Main health check with all services
- `GET /health/detailed` - Detailed health check with system info
- `GET /health/live` - Simple liveness probe
- `GET /health/ready` - Readiness probe

## Authentication Types

### 1. User Authentication
**Header**: `Authorization: Bearer <jwt_token>`
**Middleware**: `authenticateToken`

### 2. Admin Authentication
**Header**: `Authorization: Bearer <admin_jwt_token>`
**Middleware**: `authenticateAdminToken`

### 3. Internal Service Authentication
**Headers**: 
- `X-Service-Key: <internal_service_key>`
- `X-Internal-Service: true`
**Middleware**: `authenticateInternalService`

## Rate Limiting

- **Auth Routes**: Special auth rate limiting applied
- **Assessment Routes**: Assessment-specific rate limiting applied
- **General**: Global rate limiting applied to all routes

## Environment Variables Required

- `INTERNAL_SERVICE_KEY` - Key for internal service authentication
- `JWT_SECRET` - Secret for JWT token verification
- `ADMIN_JWT_SECRET` - Secret for admin JWT token verification

## Changes Made

1. ✅ Added missing auth routes: `/register/batch`, internal token management, batch registration endpoints
2. ✅ Added missing archive routes: internal result creation, batch processing, demographics endpoints
3. ✅ Added missing assessment routes: `/queue/status`, test endpoints, root endpoint
4. ✅ Created dedicated internal service routes with proper authentication
5. ✅ Added internal service authentication middleware
6. ✅ Separated user, admin, and internal service routes properly
7. ✅ Maintained backward compatibility with existing routes

## Testing Recommendations

1. Test all public endpoints without authentication
2. Test protected endpoints with valid user tokens
3. Test admin endpoints with valid admin tokens and proper roles
4. Test internal endpoints with valid service keys
5. Test rate limiting functionality
6. Test error handling for invalid authentication
7. Test proxy functionality to backend services
