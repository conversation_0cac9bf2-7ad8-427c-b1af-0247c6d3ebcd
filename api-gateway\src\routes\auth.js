const express = require('express');
const proxy = require('express-http-proxy');
const { authRateLimit } = require('../middleware/rateLimiter');
const { authenticateToken } = require('../middleware/auth');
const services = require('../config/services');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting untuk auth endpoints
router.use(authRateLimit);

/**
 * Proxy configuration untuk auth service
 */
const authProxy = proxy(services.auth.url, {
  timeout: services.auth.timeout,
  proxyReqPathResolver: (req) => {
    const path = `/auth${req.url}`;
    logger.info(`Proxying auth request to: ${services.auth.url}${path}`);
    return path;
  },
  proxyReqOptDecorator: (proxyReqOpts, srcReq) => {
    // Forward original headers
    proxyReqOpts.headers = {
      ...proxyReqOpts.headers,
      'X-Forwarded-For': srcReq.ip,
      'X-Forwarded-Proto': srcReq.protocol,
      'X-Forwarded-Host': srcReq.get('host'),
      'X-Original-URL': srcReq.originalUrl
    };
    return proxyReqOpts;
  },
  userResDecorator: (proxyRes, proxyResData, userReq, userRes) => {
    try {
      const data = JSON.parse(proxyResData.toString('utf8'));
      
      // Log auth events
      if (userReq.path === '/register' && userRes.statusCode === 201) {
        logger.info('User registration successful', {
          email: data.data?.user?.email,
          userId: data.data?.user?.id,
          ip: userReq.ip
        });
      } else if (userReq.path === '/login' && userRes.statusCode === 200) {
        logger.info('User login successful', {
          email: data.data?.user?.email,
          userId: data.data?.user?.id,
          ip: userReq.ip
        });
      }
      
      return JSON.stringify(data);
    } catch (error) {
      logger.error('Error parsing auth service response', {
        error: error.message,
        path: userReq.path
      });
      return proxyResData;
    }
  },
  proxyErrorHandler: (err, res, next) => {
    logger.error('Auth service proxy error', {
      error: err.message,
      code: err.code
    });
    
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Authentication service is temporarily unavailable'
      }
    });
  }
});

// Public auth routes (no authentication required)
router.post('/register', authProxy);
router.post('/register/batch', authProxy);
router.post('/login', authProxy);

// Protected auth routes (authentication required)
router.get('/profile', authenticateToken, authProxy);
router.put('/profile', authenticateToken, authProxy);
router.post('/change-password', authenticateToken, authProxy);
router.post('/logout', authenticateToken, authProxy);

// Token management routes
router.get('/token-balance', authenticateToken, authProxy);

module.exports = router;
