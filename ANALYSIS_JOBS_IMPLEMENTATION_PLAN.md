# Analysis Jobs Implementation Plan

## 📋 Overview

Dokumen ini menjelaskan rencana implementasi untuk menambahkan tabel `analysis_jobs` ke dalam sistem ATMA Backend. <PERSON>juan utama adalah untuk menyimpan dan melacak `jobId` secara persisten di database, yang saat ini hanya ada di memory.

### 🎯 Tujuan
- Menyimpan `jobId` secara persisten di database
- Meningkatkan tracking dan monitoring job lifecycle
- Memungkinkan recovery job state setelah restart
- Mendukung scalability dengan multiple service instances

### 🗄️ Database Schema

```sql
-- Tabel analysis_jobs akan dibuat di schema archive
CREATE TABLE archive.analysis_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'queued',
    result_id UUID NULL,
    assessment_data JSONB NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE NULL,
    
    CONSTRAINT fk_analysis_jobs_user_id FOREIGN KEY (user_id) REFERENCES auth.users(id),
    CONSTRAINT fk_analysis_jobs_result_id FOREIGN KEY (result_id) REFERENCES archive.analysis_results(id),
    CONSTRAINT chk_analysis_jobs_status CHECK (status IN ('queued', 'processing', 'completed', 'failed'))
);

-- Indexes untuk performa
CREATE INDEX idx_analysis_jobs_job_id ON archive.analysis_jobs(job_id);
CREATE INDEX idx_analysis_jobs_user_id ON archive.analysis_jobs(user_id);
CREATE INDEX idx_analysis_jobs_status ON archive.analysis_jobs(status);
CREATE INDEX idx_analysis_jobs_created_at ON archive.analysis_jobs(created_at);
CREATE INDEX idx_analysis_jobs_user_status ON archive.analysis_jobs(user_id, status);
```

---

## 🏗️ Archive Service Implementation

### 📁 File Structure Changes

```
archive-service/
├── src/
│   ├── models/
│   │   ├── AnalysisJob.js          # [NEW] Model untuk analysis_jobs
│   │   └── index.js                # [MODIFY] Tambah import AnalysisJob
│   ├── services/
│   │   ├── analysisJobsService.js  # [NEW] Service untuk manage jobs
│   │   └── resultsService.js       # [MODIFY] Integrasi dengan jobs
│   ├── routes/
│   │   └── archive.js              # [MODIFY] Tambah endpoints jobs
│   └── utils/
│       └── validation.js           # [MODIFY] Tambah validation schema
```

### 🔧 Implementation Sections

#### Section 1: Model Creation
**File: `src/models/AnalysisJob.js`**

```javascript
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AnalysisJob = sequelize.define('AnalysisJob', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  job_id: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    field: 'job_id'
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'user_id'
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'queued',
    validate: {
      isIn: [['queued', 'processing', 'completed', 'failed']]
    }
  },
  result_id: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'result_id'
  },
  assessment_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    field: 'assessment_data'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message'
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'completed_at'
  }
}, {
  tableName: 'analysis_jobs',
  schema: 'archive',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  underscored: true
});
```

**⚠️ Yang Harus Diperhatikan:**
- Pastikan foreign key constraints sesuai dengan existing tables
- Validasi status harus konsisten dengan business logic
- Index pada job_id untuk performa query

#### Section 2: Service Layer
**File: `src/services/analysisJobsService.js`**

```javascript
const AnalysisJob = require('../models/AnalysisJob');
const logger = require('../utils/logger');

class AnalysisJobsService {
  async createJob(jobData) {
    try {
      const job = await AnalysisJob.create(jobData);
      logger.info('Analysis job created', { jobId: job.job_id });
      return job;
    } catch (error) {
      logger.error('Failed to create analysis job', { error: error.message });
      throw error;
    }
  }

  async updateJobStatus(jobId, status, additionalData = {}) {
    try {
      const updateData = { status, ...additionalData };
      if (status === 'completed' || status === 'failed') {
        updateData.completed_at = new Date();
      }
      
      const [updatedRows] = await AnalysisJob.update(updateData, {
        where: { job_id: jobId }
      });
      
      if (updatedRows === 0) {
        throw new Error(`Job with ID ${jobId} not found`);
      }
      
      logger.info('Analysis job status updated', { jobId, status });
      return await this.getJobByJobId(jobId);
    } catch (error) {
      logger.error('Failed to update job status', { jobId, error: error.message });
      throw error;
    }
  }
}
```

**⚠️ Yang Harus Diperhatikan:**
- Error handling yang robust untuk database operations
- Logging yang konsisten untuk debugging
- Transaction handling untuk operasi kompleks
- Validation input sebelum database operations

#### Section 3: API Endpoints
**File: `src/routes/archive.js` (Tambahan)**

```javascript
// GET /archive/jobs/:jobId - Get job status
router.get('/jobs/:jobId', auth, async (req, res, next) => {
  try {
    const { jobId } = req.params;
    const job = await analysisJobsService.getJobByJobId(jobId);
    
    if (!job) {
      return sendNotFound(res, 'Job not found');
    }
    
    // Check if user owns the job
    if (job.user_id !== req.user.id && req.user.role !== 'admin') {
      return sendError(res, 'FORBIDDEN', 'Access denied', {}, 403);
    }
    
    return sendSuccess(res, 'Job retrieved successfully', job);
  } catch (error) {
    next(error);
  }
});

// PUT /archive/jobs/:jobId/status - Update job status (Internal only)
router.put('/jobs/:jobId/status', serviceAuth, async (req, res, next) => {
  try {
    const { jobId } = req.params;
    const { status, result_id, error_message } = req.body;
    
    const job = await analysisJobsService.updateJobStatus(jobId, status, {
      result_id,
      error_message
    });
    
    return sendSuccess(res, 'Job status updated successfully', job);
  } catch (error) {
    next(error);
  }
});
```

**⚠️ Yang Harus Diperhatikan:**
- Authentication dan authorization yang proper
- Input validation menggunakan Joi schema
- Rate limiting untuk prevent abuse
- Consistent error response format

---

## ⚙️ Analysis Worker Implementation

### 📁 File Changes

```
analysis-worker/
├── src/
│   ├── services/
│   │   ├── archiveService.js       # [MODIFY] Tambah job management
│   │   └── analysisJobsService.js  # [NEW] Service untuk job operations
│   └── worker.js                   # [MODIFY] Update job tracking flow
```

### 🔧 Implementation Sections

#### Section 1: Archive Service Enhancement
**File: `src/services/archiveService.js` (Modifikasi)**

```javascript
// Tambah fungsi untuk create job
const createAnalysisJob = async (jobId, userId, assessmentData) => {
  try {
    logger.info('Creating analysis job', { jobId, userId });

    const requestBody = {
      job_id: jobId,
      user_id: userId,
      assessment_data: assessmentData,
      status: 'processing'
    };

    const response = await archiveClient.post('/archive/jobs', requestBody);
    
    logger.info('Analysis job created successfully', {
      jobId,
      userId,
      jobDbId: response.data.data.id
    });

    return response.data.data;
  } catch (error) {
    logger.error('Failed to create analysis job', {
      jobId,
      userId,
      error: error.message
    });
    // Don't throw error - allow processing to continue
    return null;
  }
};

// Tambah fungsi untuk update job status
const updateAnalysisJobStatus = async (jobId, status, additionalData = {}) => {
  try {
    logger.info('Updating analysis job status', { jobId, status });

    const requestBody = { status, ...additionalData };
    const response = await archiveClient.put(`/archive/jobs/${jobId}/status`, requestBody);
    
    logger.info('Analysis job status updated successfully', { jobId, status });
    return response.data.data;
  } catch (error) {
    logger.error('Failed to update analysis job status', {
      jobId,
      status,
      error: error.message
    });
    // Don't throw error - allow processing to continue
    return null;
  }
};
```

**⚠️ Yang Harus Diperhatikan:**
- Graceful degradation jika archive service tidak tersedia
- Jangan stop processing jika job tracking gagal
- Proper error logging untuk debugging
- Maintain backward compatibility

#### Section 2: Worker Flow Update
**File: `src/worker.js` (Modifikasi)**

```javascript
// Update processMessage function
const processMessage = async (message) => {
  const { jobId, userId, userEmail, assessmentData } = JSON.parse(message.content.toString());
  
  try {
    logger.info('Processing assessment job', { jobId, userId });

    // 1. Create job entry in database
    await archiveService.createAnalysisJob(jobId, userId, assessmentData);

    // 2. Update job status to processing
    await archiveService.updateAnalysisJobStatus(jobId, 'processing');

    // 3. Process assessment (existing logic)
    const analysisResult = await processAssessment(assessmentData, jobId);

    // 4. Save result to archive
    const savedResult = await archiveService.saveAnalysisResult(
      userId, 
      assessmentData, 
      analysisResult, 
      jobId
    );

    // 5. Update job status to completed with result_id
    await archiveService.updateAnalysisJobStatus(jobId, 'completed', {
      result_id: savedResult.id
    });

    // 6. Send notifications (existing logic)
    await notificationService.sendAnalysisCompleteNotification(userId, jobId, savedResult.id);
    await notificationService.updateAssessmentJobStatus(jobId, savedResult.id, 'completed');

    logger.info('Job processed successfully', { jobId, resultId: savedResult.id });

  } catch (error) {
    logger.error('Job processing failed', { jobId, error: error.message });

    // Update job status to failed
    await archiveService.updateAnalysisJobStatus(jobId, 'failed', {
      error_message: error.message
    });

    // Handle failure (existing logic)
    await handleJobFailure(jobId, userId, error);
  }
};
```

**⚠️ Yang Harus Diperhatikan:**
- Maintain existing error handling logic
- Ensure job status updates don't block main processing
- Add proper rollback mechanism for failed jobs
- Keep existing notification flow intact

---

## 📊 Assessment Service Implementation

### 📁 File Changes

```
assessment-service/
├── src/
│   ├── routes/
│   │   └── assessments.js          # [MODIFY] Update callback handlers
│   ├── jobs/
│   │   └── jobTracker.js           # [MODIFY] Optional sync with database
│   └── services/
│       └── archiveService.js       # [NEW] Service untuk komunikasi dengan archive
```

### 🔧 Implementation Sections

#### Section 1: Callback Handler Enhancement
**File: `src/routes/assessments.js` (Modifikasi)**

```javascript
// Update callback completed handler
router.post('/callback/completed', serviceAuth, async (req, res, next) => {
  try {
    const { jobId, resultId, status } = req.body;

    // Validate required fields
    if (!jobId || !resultId || !status) {
      return sendError(res, 'VALIDATION_ERROR', 'Missing required fields', {}, 400);
    }

    // Update local job tracker
    const updatedJob = jobTracker.updateJobStatus(jobId, status, 100);

    if (!updatedJob) {
      return sendNotFound(res, 'Job not found in local tracker');
    }

    // Optional: Sync with archive service database
    try {
      await archiveService.syncJobStatus(jobId, status, { result_id: resultId });
    } catch (syncError) {
      logger.warn('Failed to sync job status with archive', {
        jobId,
        error: syncError.message
      });
      // Don't fail the request if sync fails
    }

    logger.info('Job status updated via callback', {
      jobId,
      resultId,
      status,
      userId: updatedJob.userId
    });

    return sendSuccess(res, 'Job status updated successfully', {
      jobId,
      status: updatedJob.status,
      progress: updatedJob.progress,
      updatedAt: updatedJob.updatedAt
    });
  } catch (error) {
    next(error);
  }
});
```

**⚠️ Yang Harus Diperhatikan:**
- Maintain existing API contract
- Sync dengan database bersifat optional
- Jangan fail request jika sync gagal
- Keep backward compatibility

#### Section 2: Archive Service Integration
**File: `src/services/archiveService.js` (Baru)**

```javascript
const axios = require('axios');
const logger = require('../utils/logger');

const ARCHIVE_SERVICE_URL = process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002';

const archiveClient = axios.create({
  baseURL: ARCHIVE_SERVICE_URL,
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json',
    'X-Internal-Service': 'true',
    'X-Service-Key': process.env.INTERNAL_SERVICE_KEY
  }
});

const syncJobStatus = async (jobId, status, additionalData = {}) => {
  try {
    const response = await archiveClient.put(`/archive/jobs/${jobId}/status`, {
      status,
      ...additionalData
    });
    
    logger.info('Job status synced with archive', { jobId, status });
    return response.data;
  } catch (error) {
    logger.error('Failed to sync job status', {
      jobId,
      status,
      error: error.message
    });
    throw error;
  }
};

module.exports = {
  syncJobStatus
};
```

**⚠️ Yang Harus Diperhatikan:**
- Short timeout untuk avoid blocking
- Proper error handling dan logging
- Optional integration - tidak wajib berhasil
- Use internal service authentication

---

## 🔔 Notification Service

### 📝 Implementation Note

**Notification Service TIDAK memerlukan perubahan** karena:
- Sudah menerima `jobId` dengan benar dari analysis-worker
- Flow WebSocket notification sudah berfungsi dengan baik
- API contract tidak berubah

**⚠️ Yang Harus Diperhatikan:**
- Monitor untuk memastikan tidak ada breaking changes
- Verify WebSocket notifications masih berfungsi
- Check logging untuk debugging jika diperlukan

---

## 🚀 Deployment Strategy

### Phase 1: Database Setup
1. **Run SQL script** untuk create table dan indexes
2. **Verify table structure** dan constraints
3. **Test database connectivity** dari archive-service

### Phase 2: Archive Service Deployment
1. **Deploy model dan service changes**
2. **Test new API endpoints**
3. **Verify backward compatibility**

### Phase 3: Analysis Worker Deployment
1. **Deploy enhanced job tracking**
2. **Test end-to-end flow**
3. **Monitor job processing**

### Phase 4: Assessment Service Deployment
1. **Deploy optional sync features**
2. **Test callback handlers**
3. **Monitor integration**

### 📊 Monitoring Points

- **Database performance** - Query execution time
- **API response time** - Archive service endpoints
- **Job success rate** - Completed vs failed jobs
- **Error rates** - Service integration failures
- **Data consistency** - Job status sync accuracy

---

## ⚠️ Critical Considerations

### 🔒 Data Integrity
- Use database transactions untuk operasi kompleks
- Implement proper rollback mechanisms
- Ensure foreign key constraints

### 🚀 Performance
- Monitor database load setelah implementasi
- Optimize queries dengan proper indexing
- Consider connection pooling

### 🛡️ Error Handling
- Graceful degradation jika database tidak tersedia
- Fallback ke existing in-memory tracking
- Comprehensive logging untuk debugging

### 🔄 Backward Compatibility
- Jangan mengubah existing API contracts
- Maintain existing response formats
- Ensure existing flows tetap berfungsi

### 📈 Scalability
- Design untuk multiple service instances
- Consider distributed locking jika diperlukan
- Plan untuk future enhancements
