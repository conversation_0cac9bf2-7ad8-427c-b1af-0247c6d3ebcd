# Configuration Guide - Token Counting System

## Overview

This guide provides detailed configuration instructions for the Token Counting and Usage Tracking System in the analysis-worker service.

## Environment Configuration

### Required Variables

```env
# Token Counting Configuration
ENABLE_TOKEN_COUNTING=true

# Usage data retention settings (in days)
TOKEN_USAGE_RETENTION_DAYS=30

# Token pricing configuration (per 1000 tokens in USD)
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003

# Token counting performance settings
TOKEN_COUNT_TIMEOUT=5000
ENABLE_TOKEN_COUNT_FALLBACK=true
```

### Configuration Profiles

#### Development Environment

```env
# Development Configuration
NODE_ENV=development
ENABLE_TOKEN_COUNTING=true
USE_MOCK_MODEL=true
TOKEN_USAGE_RETENTION_DAYS=7
TOKEN_COUNT_TIMEOUT=3000
ENABLE_TOKEN_COUNT_FALLBACK=true
LOG_LEVEL=debug

# Development pricing (for testing)
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003
```

#### Production Environment

```env
# Production Configuration
NODE_ENV=production
ENABLE_TOKEN_COUNTING=true
USE_MOCK_MODEL=false
TOKEN_USAGE_RETENTION_DAYS=90
TOKEN_COUNT_TIMEOUT=5000
ENABLE_TOKEN_COUNT_FALLBACK=true
LOG_LEVEL=info

# Production pricing (current Gemini 2.5 Flash rates)
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003
```

#### Testing Environment

```env
# Testing Configuration
NODE_ENV=test
ENABLE_TOKEN_COUNTING=true
USE_MOCK_MODEL=true
TOKEN_USAGE_RETENTION_DAYS=1
TOKEN_COUNT_TIMEOUT=1000
ENABLE_TOKEN_COUNT_FALLBACK=true
LOG_LEVEL=warn

# Test pricing
INPUT_TOKEN_PRICE_PER_1K=0.000001
OUTPUT_TOKEN_PRICE_PER_1K=0.000001
```

## Configuration Options Reference

### Core Settings

#### `ENABLE_TOKEN_COUNTING`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Master switch for token counting system
- **Values**: `true` | `false`
- **Impact**: When disabled, no token counting or usage tracking occurs

```env
# Enable token counting
ENABLE_TOKEN_COUNTING=true

# Disable token counting (emergency fallback)
ENABLE_TOKEN_COUNTING=false
```

#### `TOKEN_USAGE_RETENTION_DAYS`
- **Type**: Integer
- **Default**: `30`
- **Description**: Number of days to retain usage data
- **Range**: `1-365`
- **Impact**: Affects memory usage and historical reporting capability

```env
# Short retention for development
TOKEN_USAGE_RETENTION_DAYS=7

# Standard retention for production
TOKEN_USAGE_RETENTION_DAYS=30

# Extended retention for analytics
TOKEN_USAGE_RETENTION_DAYS=90
```

### Pricing Configuration

#### `INPUT_TOKEN_PRICE_PER_1K`
- **Type**: Float
- **Default**: `0.000075`
- **Description**: Cost per 1000 input tokens in USD
- **Current Rate**: Gemini 2.5 Flash pricing (as of 2024)

#### `OUTPUT_TOKEN_PRICE_PER_1K`
- **Type**: Float
- **Default**: `0.0003`
- **Description**: Cost per 1000 output tokens in USD
- **Current Rate**: Gemini 2.5 Flash pricing (as of 2024)

```env
# Current Gemini 2.5 Flash pricing
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003

# Custom pricing for different models
INPUT_TOKEN_PRICE_PER_1K=0.0001
OUTPUT_TOKEN_PRICE_PER_1K=0.0005
```

### Performance Settings

#### `TOKEN_COUNT_TIMEOUT`
- **Type**: Integer (milliseconds)
- **Default**: `5000`
- **Description**: Timeout for token counting API calls
- **Range**: `1000-30000`
- **Recommendation**: 3000-5000ms for most environments

```env
# Fast timeout for development
TOKEN_COUNT_TIMEOUT=3000

# Standard timeout for production
TOKEN_COUNT_TIMEOUT=5000

# Extended timeout for slow networks
TOKEN_COUNT_TIMEOUT=10000
```

#### `ENABLE_TOKEN_COUNT_FALLBACK`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable fallback estimation when API fails
- **Recommendation**: Always `true` for production

```env
# Enable fallback (recommended)
ENABLE_TOKEN_COUNT_FALLBACK=true

# Disable fallback (testing only)
ENABLE_TOKEN_COUNT_FALLBACK=false
```

## Configuration Validation

### Startup Validation

The system validates configuration on startup:

```javascript
// Configuration validation example
const config = {
  enableTokenCounting: process.env.ENABLE_TOKEN_COUNTING === 'true',
  retentionDays: parseInt(process.env.TOKEN_USAGE_RETENTION_DAYS) || 30,
  inputTokenPrice: parseFloat(process.env.INPUT_TOKEN_PRICE_PER_1K) || 0.000075,
  outputTokenPrice: parseFloat(process.env.OUTPUT_TOKEN_PRICE_PER_1K) || 0.0003,
  timeout: parseInt(process.env.TOKEN_COUNT_TIMEOUT) || 5000,
  enableFallback: process.env.ENABLE_TOKEN_COUNT_FALLBACK !== 'false'
};

// Validation checks
if (config.retentionDays < 1 || config.retentionDays > 365) {
  throw new Error('TOKEN_USAGE_RETENTION_DAYS must be between 1 and 365');
}

if (config.timeout < 1000 || config.timeout > 30000) {
  throw new Error('TOKEN_COUNT_TIMEOUT must be between 1000 and 30000');
}
```

### Runtime Configuration Changes

Some settings can be changed at runtime:

```javascript
const UsageTracker = require('./src/services/usageTracker');
const usageTracker = new UsageTracker();

// Update retention period
usageTracker.updateRetentionDays(60);

// Update pricing
usageTracker.updatePricing({
  inputTokenPrice: 0.0001,
  outputTokenPrice: 0.0004
});
```

## Configuration Examples

### Scenario 1: High-Volume Production

```env
# High-volume production environment
NODE_ENV=production
ENABLE_TOKEN_COUNTING=true
USE_MOCK_MODEL=false

# Extended retention for analytics
TOKEN_USAGE_RETENTION_DAYS=90

# Optimized performance settings
TOKEN_COUNT_TIMEOUT=3000
ENABLE_TOKEN_COUNT_FALLBACK=true

# Current Gemini pricing
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003

# Performance optimization
WORKER_CONCURRENCY=20
LOG_LEVEL=info
```

### Scenario 2: Development with Cost Control

```env
# Development with cost monitoring
NODE_ENV=development
ENABLE_TOKEN_COUNTING=true
USE_MOCK_MODEL=false

# Short retention for development
TOKEN_USAGE_RETENTION_DAYS=7

# Quick timeouts for development
TOKEN_COUNT_TIMEOUT=2000
ENABLE_TOKEN_COUNT_FALLBACK=true

# Higher pricing for cost awareness
INPUT_TOKEN_PRICE_PER_1K=0.0001
OUTPUT_TOKEN_PRICE_PER_1K=0.0005

# Detailed logging
LOG_LEVEL=debug
```

### Scenario 3: Testing Environment

```env
# Automated testing environment
NODE_ENV=test
ENABLE_TOKEN_COUNTING=true
USE_MOCK_MODEL=true

# Minimal retention for testing
TOKEN_USAGE_RETENTION_DAYS=1

# Fast timeouts for quick tests
TOKEN_COUNT_TIMEOUT=1000
ENABLE_TOKEN_COUNT_FALLBACK=true

# Minimal pricing for testing
INPUT_TOKEN_PRICE_PER_1K=0.000001
OUTPUT_TOKEN_PRICE_PER_1K=0.000001

# Minimal logging for clean test output
LOG_LEVEL=error
```

### Scenario 4: Cost-Optimized Environment

```env
# Cost-optimized configuration
NODE_ENV=production
ENABLE_TOKEN_COUNTING=true
USE_MOCK_MODEL=false

# Standard retention
TOKEN_USAGE_RETENTION_DAYS=30

# Aggressive timeouts to avoid costs on slow calls
TOKEN_COUNT_TIMEOUT=2000
ENABLE_TOKEN_COUNT_FALLBACK=true

# Accurate pricing for cost monitoring
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003

# Reduced concurrency to control costs
WORKER_CONCURRENCY=5
```

## Configuration Management

### Environment-Specific Files

Create separate configuration files for different environments:

```bash
# Development
.env.development

# Production  
.env.production

# Testing
.env.test

# Local development
.env.local
```

### Configuration Loading

```javascript
// Load environment-specific configuration
const dotenv = require('dotenv');
const path = require('path');

const env = process.env.NODE_ENV || 'development';
const envFile = `.env.${env}`;

// Load base configuration
dotenv.config();

// Load environment-specific configuration
if (require('fs').existsSync(envFile)) {
  dotenv.config({ path: envFile });
}
```

### Configuration Validation Script

Create a validation script:

```javascript
// scripts/validate-config.js
const validateConfiguration = () => {
  const errors = [];
  
  // Check required variables
  const required = [
    'GOOGLE_AI_API_KEY',
    'ENABLE_TOKEN_COUNTING',
    'TOKEN_USAGE_RETENTION_DAYS',
    'INPUT_TOKEN_PRICE_PER_1K',
    'OUTPUT_TOKEN_PRICE_PER_1K'
  ];
  
  required.forEach(key => {
    if (!process.env[key]) {
      errors.push(`Missing required environment variable: ${key}`);
    }
  });
  
  // Validate ranges
  const retentionDays = parseInt(process.env.TOKEN_USAGE_RETENTION_DAYS);
  if (retentionDays < 1 || retentionDays > 365) {
    errors.push('TOKEN_USAGE_RETENTION_DAYS must be between 1 and 365');
  }
  
  const timeout = parseInt(process.env.TOKEN_COUNT_TIMEOUT);
  if (timeout < 1000 || timeout > 30000) {
    errors.push('TOKEN_COUNT_TIMEOUT must be between 1000 and 30000');
  }
  
  // Validate pricing
  const inputPrice = parseFloat(process.env.INPUT_TOKEN_PRICE_PER_1K);
  const outputPrice = parseFloat(process.env.OUTPUT_TOKEN_PRICE_PER_1K);
  
  if (inputPrice < 0 || inputPrice > 1) {
    errors.push('INPUT_TOKEN_PRICE_PER_1K must be between 0 and 1');
  }
  
  if (outputPrice < 0 || outputPrice > 1) {
    errors.push('OUTPUT_TOKEN_PRICE_PER_1K must be between 0 and 1');
  }
  
  return errors;
};

// Run validation
const errors = validateConfiguration();
if (errors.length > 0) {
  console.error('Configuration validation failed:');
  errors.forEach(error => console.error(`  - ${error}`));
  process.exit(1);
} else {
  console.log('Configuration validation passed');
}
```

## Monitoring Configuration

### Configuration Metrics

Monitor configuration effectiveness:

```javascript
// Configuration monitoring
const configMetrics = {
  tokenCountingEnabled: process.env.ENABLE_TOKEN_COUNTING === 'true',
  retentionDays: parseInt(process.env.TOKEN_USAGE_RETENTION_DAYS),
  timeout: parseInt(process.env.TOKEN_COUNT_TIMEOUT),
  fallbackEnabled: process.env.ENABLE_TOKEN_COUNT_FALLBACK !== 'false',
  
  // Runtime metrics
  fallbackUsageRate: 0, // Percentage of requests using fallback
  averageTokenCountTime: 0, // Average time for token counting
  configurationErrors: 0 // Number of configuration-related errors
};
```

### Health Checks

```javascript
// Configuration health check
const healthCheck = () => {
  const health = {
    status: 'healthy',
    checks: {
      tokenCountingEnabled: process.env.ENABLE_TOKEN_COUNTING === 'true',
      validRetentionPeriod: true,
      validTimeout: true,
      validPricing: true
    }
  };
  
  // Check retention period
  const retention = parseInt(process.env.TOKEN_USAGE_RETENTION_DAYS);
  if (retention < 1 || retention > 365) {
    health.checks.validRetentionPeriod = false;
    health.status = 'unhealthy';
  }
  
  // Check timeout
  const timeout = parseInt(process.env.TOKEN_COUNT_TIMEOUT);
  if (timeout < 1000 || timeout > 30000) {
    health.checks.validTimeout = false;
    health.status = 'unhealthy';
  }
  
  // Check pricing
  const inputPrice = parseFloat(process.env.INPUT_TOKEN_PRICE_PER_1K);
  const outputPrice = parseFloat(process.env.OUTPUT_TOKEN_PRICE_PER_1K);
  if (inputPrice < 0 || outputPrice < 0) {
    health.checks.validPricing = false;
    health.status = 'unhealthy';
  }
  
  return health;
};
```

## Troubleshooting Configuration

### Common Configuration Issues

#### Issue: Token counting not working
```env
# Check these settings
ENABLE_TOKEN_COUNTING=true  # Must be 'true'
GOOGLE_AI_API_KEY=your_key  # Must be valid
USE_MOCK_MODEL=false        # For real API calls
```

#### Issue: High memory usage
```env
# Reduce retention period
TOKEN_USAGE_RETENTION_DAYS=7  # Instead of 90

# Or disable token counting temporarily
ENABLE_TOKEN_COUNTING=false
```

#### Issue: Slow performance
```env
# Reduce timeout
TOKEN_COUNT_TIMEOUT=2000  # Instead of 5000

# Enable fallback
ENABLE_TOKEN_COUNT_FALLBACK=true
```

#### Issue: Inaccurate cost estimates
```env
# Update pricing to current rates
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003

# Check Gemini pricing documentation for latest rates
```

### Configuration Debugging

Enable debug mode for configuration issues:

```env
LOG_LEVEL=debug
```

This will log:
- Configuration loading details
- Validation results
- Runtime configuration changes
- Performance metrics

### Configuration Reset

To reset to default configuration:

```bash
# Backup current configuration
cp .env .env.backup

# Reset to defaults
cat > .env << EOF
# Default Token Counting Configuration
ENABLE_TOKEN_COUNTING=true
TOKEN_USAGE_RETENTION_DAYS=30
INPUT_TOKEN_PRICE_PER_1K=0.000075
OUTPUT_TOKEN_PRICE_PER_1K=0.0003
TOKEN_COUNT_TIMEOUT=5000
ENABLE_TOKEN_COUNT_FALLBACK=true
EOF
```

## Best Practices

### Configuration Management

1. **Use Environment-Specific Files**: Separate configurations for dev/test/prod
2. **Validate on Startup**: Always validate configuration before starting services
3. **Monitor Configuration**: Track configuration effectiveness and adjust as needed
4. **Document Changes**: Keep a changelog of configuration modifications
5. **Test Configuration**: Test configuration changes in non-production environments first

### Security

1. **Protect API Keys**: Never commit API keys to version control
2. **Use Environment Variables**: Store sensitive configuration in environment variables
3. **Rotate Keys**: Regularly rotate API keys and update configuration
4. **Audit Access**: Monitor who has access to configuration files

### Performance

1. **Tune Timeouts**: Adjust timeouts based on your network conditions
2. **Monitor Impact**: Regularly assess performance impact of token counting
3. **Optimize Retention**: Balance storage needs with performance requirements
4. **Use Fallbacks**: Always enable fallback mechanisms for production

This configuration guide provides comprehensive instructions for setting up and managing the token counting system configuration. Adjust settings based on your specific requirements and environment constraints.