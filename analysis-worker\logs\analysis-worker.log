{"level":"info","maxRecords":50,"message":"UsageTracker initialized","retentionDays":7,"service":"analysis-worker","timestamp":"2025-07-19 14:14:08","version":"1.0.0"}
{"component":"usage_tracker","health":{"criticalAlerts":0,"isHealthy":false,"warningAlerts":1},"level":"info","message":"Usage monitoring report generated","metrics":{"alertCount":1,"bufferUtilization":16,"estimatedCost":0.000149,"successRate":75,"totalRequests":8,"totalTokens":300},"reportType":"usage_monitoring","service":"analysis-worker","structured":true,"timeframe":"daily","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"component":"usage_tracker","level":"info","message":"Real-time usage metrics","metrics":{"current":{"last24Hours":{"cost":0.000149,"requests":8,"successRate":75,"tokens":300},"lastHour":{"cost":0.000149,"requests":8,"successRate":75,"tokens":300}},"performance":{"averageResponseTime":1428,"errorRate":25,"tokenCountingLatency":214},"system":{"bufferUtilization":16,"isHealthy":false,"memoryUsageKB":2,"status":"healthy"},"timestamp":"2025-07-19T07:14:09.038Z"},"service":"analysis-worker","structured":true,"timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"exportSize":2195,"format":"json","level":"info","message":"Usage data exported for monitoring","recordCount":8,"service":"analysis-worker","structured":true,"timeframe":"daily","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"level":"info","message":"Usage statistics reset successfully","service":"analysis-worker","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"level":"info","message":"UsageTracker destroyed successfully","service":"analysis-worker","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
